# 🚀 FixGuru CodePipeline CI/CD 部署指南

## 📋 概述

本指南基于现有的FixGuru项目，帮您设置一个完整的CI/CD流水线，使用AWS CodePipeline + CodeBuild + SAM自动化构建和部署Spring Boot Lambda应用。

## 🏗️ 架构图

```
GitHub (DayuHZ/FixGuru_Service) → CodePipeline → CodeBuild → CloudFormation → Lambda + API Gateway + DynamoDB
        ↓                              ↓            ↓             ↓                    ↓
    代码推送(master)                自动触发      Maven构建    SAM部署           FixGuru应用运行
```

## 🔧 前置条件

### 1. AWS环境
- ✅ AWS CLI已安装并配置
- ✅ 具有足够权限的AWS账户
- ✅ 部署区域：us-west-1
- ✅ 现有的S3部署桶：fixguru-lambda-deployment-1753610168

### 2. GitHub仓库
- ✅ GitHub仓库：https://github.com/DayuHZ/FixGuru_Service
- ✅ Personal Access Token（需要repo权限）
- ✅ 主分支：master

### 3. 本地环境
- ✅ Java 17
- ✅ Maven 3.9+
- ✅ Git
- ✅ 现有的项目代码和配置

## 📝 快速部署步骤

### 步骤1：一键部署CI/CD流水线

使用项目提供的部署脚本：

```bash
# 部署开发环境流水线
./scripts/deploy-pipeline.sh dev

# 部署测试环境流水线
./scripts/deploy-pipeline.sh test

# 部署生产环境流水线
./scripts/deploy-pipeline.sh prod
```

脚本会自动完成以下操作：
- ✅ 检查AWS凭证和必需工具
- ✅ 创建或验证GitHub访问令牌
- ✅ 创建或验证JWT密钥
- ✅ 部署CodePipeline基础设施
- ✅ 配置所有必需的IAM角色和权限

### 步骤2：手动配置GitHub访问令牌（如需要）

如果脚本提示需要GitHub Token：

1. 访问：https://github.com/settings/tokens
2. 点击"Generate new token (classic)"
3. 选择以下权限：
   - ✅ `repo` (完整仓库访问权限)
   - ✅ `admin:repo_hook` (仓库钩子权限)
4. 复制生成的token
5. 在脚本提示时输入token

### 步骤3：验证部署

部署完成后，检查以下内容：

```bash
# 检查Pipeline状态
aws codepipeline get-pipeline-state --name fixguru-pipeline-dev --region us-west-1

# 查看Pipeline URL（从部署输出获取）
echo "Pipeline URL: https://console.aws.amazon.com/codesuite/codepipeline/pipelines/fixguru-pipeline-dev/view"
```

### 步骤4：触发首次构建

推送代码到GitHub触发首次构建：

```bash
git add .
git commit -m "Setup CI/CD pipeline"
git push origin master
```

## 🔄 流水线工作流程详解

### 1. Source阶段 (GitHub集成)
- **触发条件**：监听master分支的代码变更
- **数据源**：https://github.com/DayuHZ/FixGuru_Service
- **输出**：源代码压缩包

### 2. Build阶段 (CodeBuild)
基于项目中的 `buildspec.yml` 文件执行：

**安装阶段**：
- Java 17 (Amazon Corretto)
- Maven 3.9+
- AWS CLI & SAM CLI

**预构建阶段**：
- 创建环境变量文件 (`.env.{environment}`)
- 从Parameter Store获取JWT密钥
- 验证构建环境

**构建阶段**：
- Maven编译：`mvn clean compile`
- 运行单元测试：`mvn test`
- Maven打包：`mvn package`
- SAM构建：`sam build --use-container`

**后构建阶段**：
- SAM打包：`sam package`
- CloudFormation模板验证
- 生成部署包

### 3. Deploy阶段 (CloudFormation)
- **创建变更集**：基于packaged-template.yaml
- **执行变更集**：部署Lambda、API Gateway、DynamoDB
- **环境配置**：自动设置环境变量和权限

## 📊 监控和管理

### AWS控制台链接
- **CodePipeline**: https://us-west-1.console.aws.amazon.com/codesuite/codepipeline/
- **CodeBuild**: https://us-west-1.console.aws.amazon.com/codesuite/codebuild/
- **CloudFormation**: https://us-west-1.console.aws.amazon.com/cloudformation/
- **Lambda**: https://us-west-1.console.aws.amazon.com/lambda/
- **API Gateway**: https://us-west-1.console.aws.amazon.com/apigateway/
- **DynamoDB**: https://us-west-1.console.aws.amazon.com/dynamodbv2/

### 常用管理命令

#### Pipeline管理
```bash
# 查看Pipeline状态
aws codepipeline get-pipeline-state --name fixguru-pipeline-dev --region us-west-1

# 手动触发Pipeline
aws codepipeline start-pipeline-execution --name fixguru-pipeline-dev --region us-west-1

# 查看Pipeline执行历史
aws codepipeline list-pipeline-executions --pipeline-name fixguru-pipeline-dev --region us-west-1
```

#### 应用堆栈管理
```bash
# 查看应用CloudFormation堆栈
aws cloudformation describe-stacks --stack-name fixguru-app-dev --region us-west-1

# 查看堆栈事件
aws cloudformation describe-stack-events --stack-name fixguru-app-dev --region us-west-1

# 查看堆栈输出
aws cloudformation describe-stacks --stack-name fixguru-app-dev --query 'Stacks[0].Outputs' --region us-west-1
```

#### 构建日志查看
```bash
# 查看CodeBuild项目
aws codebuild list-projects --region us-west-1

# 查看构建历史
aws codebuild list-builds-for-project --project-name fixguru-build-dev --region us-west-1

# 使用项目脚本查看日志（推荐）
./scripts/aws/monitor-pipeline.sh dev --build-logs
```

## 🔍 Pipeline监控和调试

### 实时监控脚本（推荐）

项目提供了专门的监控脚本，使用简单且功能强大：

#### 基本状态查看
```bash
# 查看Pipeline当前状态
./scripts/aws/monitor-pipeline.sh dev

# 输出示例：
# Pipeline信息:
#   名称: fixguru-pipeline-dev
#   环境: dev
#   区域: us-west-1
#   控制台: https://console.aws.amazon.com/codesuite/codepipeline/...
#
# Pipeline状态:
#   ✅ Source: Succeeded
#   ⏳ Build: InProgress
#   ⏸️  Deploy: 未执行
#
# 最新执行:
#   ✅ 状态: InProgress
#   🆔 执行ID: 12345678-1234-1234-1234-123456789012
#   ⏰ 开始时间: 2025-07-28T14:30:00Z
```

#### 实时跟踪执行
```bash
# 实时跟踪Pipeline执行过程
./scripts/aws/monitor-pipeline.sh dev --follow

# 会每30秒刷新一次状态，直到执行完成
```

#### 查看详细日志
```bash
# 查看构建日志
./scripts/aws/monitor-pipeline.sh dev --build-logs

# 查看部署日志
./scripts/aws/monitor-pipeline.sh dev --deploy-logs
```

### 故障排除脚本

当Pipeline出现问题时，使用故障排除脚本进行自动诊断：

```bash
# 自动诊断Pipeline问题
./scripts/aws/troubleshoot-pipeline.sh dev

# 会自动检查：
# 1. AWS凭证状态
# 2. Pipeline是否存在
# 3. GitHub Token有效性
# 4. JWT密钥配置
# 5. 最新执行状态
# 6. CodeBuild项目状态
# 7. 应用堆栈状态
# 8. Lambda函数状态
```

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. GitHub Token相关问题
**问题**：Pipeline无法访问GitHub仓库
**解决方案**：
```bash
# 检查token是否存在
aws secretsmanager describe-secret --secret-id github-token --region us-west-1

# 更新token
aws secretsmanager update-secret --secret-id github-token \
    --secret-string '{"token":"NEW_GITHUB_TOKEN"}' --region us-west-1
```

#### 2. 构建失败问题
**问题**：CodeBuild构建失败
**解决方案**：
- 检查 `buildspec.yml` 配置
- 查看CodeBuild详细日志
- 验证Maven依赖和Java版本

```bash
# 查看最新构建日志
BUILD_ID=$(aws codebuild list-builds-for-project --project-name fixguru-build-dev --region us-west-1 --query 'ids[0]' --output text)
aws logs get-log-events --log-group-name "/aws/codebuild/fixguru-build-dev" --log-stream-name "$BUILD_ID" --region us-west-1
```

#### 3. 部署失败问题
**问题**：CloudFormation部署失败
**解决方案**：
```bash
# 查看CloudFormation事件
aws cloudformation describe-stack-events --stack-name fixguru-app-dev --region us-west-1

# 检查IAM权限
aws iam get-role --role-name fixguru-cloudformation-role-dev
```

#### 4. Lambda函数问题
**问题**：Lambda函数运行错误
**解决方案**：
```bash
# 查看Lambda日志
aws logs tail /aws/lambda/fixguru-function-dev --region us-west-1 --since 10m

# 测试Lambda函数
aws lambda invoke --function-name fixguru-function-dev --region us-west-1 response.json
```

### 完整日志查看命令
```bash
# CodeBuild日志
aws logs describe-log-groups --log-group-name-prefix /aws/codebuild/fixguru --region us-west-1

# Lambda日志
aws logs describe-log-groups --log-group-name-prefix /aws/lambda/fixguru --region us-west-1

# API Gateway日志
aws logs describe-log-groups --log-group-name-prefix API-Gateway-Execution-Logs --region us-west-1
```

## 🔧 自定义配置

### 项目文件说明
- **`buildspec.yml`**：CodeBuild构建规范，定义构建步骤
- **`pipeline-template.yaml`**：CodePipeline基础设施模板
- **`template.yaml`**：SAM应用模板，定义Lambda和API Gateway
- **`scripts/deploy-pipeline.sh`**：一键部署脚本

### 修改构建配置
编辑 `buildspec.yml` 文件来自定义构建过程：
```yaml
# 添加额外的构建步骤
build:
  commands:
    - echo "自定义构建步骤"
    - mvn clean compile -DskipTests
    # 添加你的自定义命令
```

### 修改部署配置
编辑 `template.yaml` 文件来调整Lambda和API Gateway配置：
```yaml
# 调整Lambda配置
Globals:
  Function:
    Timeout: 60        # 调整超时时间
    MemorySize: 2048   # 调整内存大小
```

### 环境变量配置
在 `pipeline-template.yaml` 中添加环境变量：
```yaml
EnvironmentVariables:
  - Name: CUSTOM_VAR
    Value: custom_value
```

## 📈 最佳实践

### 1. 多环境部署策略
```bash
# 开发环境：master分支自动部署
./scripts/deploy-pipeline.sh dev

# 测试环境：手动触发或定时部署
./scripts/deploy-pipeline.sh test

# 生产环境：手动审批后部署
./scripts/deploy-pipeline.sh prod
```

### 2. 安全最佳实践
- ✅ 使用AWS Secrets Manager存储敏感信息
- ✅ 使用Parameter Store存储配置参数
- ✅ 最小权限原则配置IAM角色
- ✅ 启用CloudTrail记录API调用

### 3. 监控和告警
- ✅ 设置CodePipeline失败告警
- ✅ 监控Lambda函数错误率
- ✅ 配置API Gateway访问日志
- ✅ 设置DynamoDB读写容量告警

### 4. 测试集成
在 `buildspec.yml` 中集成测试：
```yaml
build:
  commands:
    - mvn test                    # 单元测试
    - mvn integration-test        # 集成测试
    - mvn verify                  # 验证测试
```

## 🎯 部署后验证

### 自动验证脚本
```bash
# 手动验证API端点
curl -X GET "https://your-api-gateway-url/api/v1/health"

# 详细健康检查
curl -X GET "https://your-api-gateway-url/api/v1/health/detailed"
```

### 验证清单
1. ✅ **Pipeline状态**：检查CodePipeline执行成功
2. ✅ **API测试**：访问API Gateway端点
3. ✅ **健康检查**：调用 `/api/v1/health` 接口
4. ✅ **API文档**：访问 `/doc.html` 页面
5. ✅ **DynamoDB**：验证表创建和数据访问
6. ✅ **日志监控**：查看CloudWatch日志

## 📞 技术支持

### 问题排查顺序
1. **Pipeline失败**：检查CodePipeline控制台
2. **构建失败**：查看CodeBuild日志
3. **部署失败**：检查CloudFormation事件
4. **应用错误**：查看Lambda函数日志
5. **API问题**：检查API Gateway日志

### 联系方式
- **项目仓库**：https://github.com/DayuHZ/FixGuru_Service
- **文档位置**：`docs/CICD部署指南.md`
- **脚本位置**：`scripts/deploy-pipeline.sh`
