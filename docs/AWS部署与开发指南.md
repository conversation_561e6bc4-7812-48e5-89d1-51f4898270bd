# AWS 部署与开发指南

## 🎯 部署原则与最佳实践

### 1. 单一堆栈管理
- ✅ 所有相关资源都在一个CloudFormation堆栈中定义
- ✅ 使用SAM模板统一管理Lambda、API Gateway、DynamoDB等资源
- ❌ 避免手动创建AWS资源
- ❌ 避免多个堆栈管理相同类型的资源

### 2. 资源命名规范
```yaml
# 推荐的命名模式
Resources:
  ApiGateway:
    Properties:
      Name: !Sub '${ProjectName}-api-${Environment}'

  LambdaFunction:
    Properties:
      FunctionName: !Sub '${ProjectName}-function-${Environment}'

  DynamoDBTable:
    Properties:
      TableName: !Sub '${Environment}_${TableName}'
```

### 3. 环境隔离
- 使用Environment参数区分不同环境
- 每个环境使用独立的堆栈
- 资源名称包含环境标识

### 4. 部署前检查清单
- [ ] 确认没有重复的资源名称
- [ ] 检查是否有旧的堆栈需要清理
- [ ] 备份重要数据（如DynamoDB表）
- [ ] 验证SAM模板语法
- [ ] 确认环境变量配置正确

## 🔧 环境变量配置

### 必需环境变量
- **AWS_REGION**: AWS区域（默认: us-west-1）
- **AWS_ACCESS_KEY_ID**: AWS访问密钥ID
- **AWS_SECRET_ACCESS_KEY**: AWS秘密访问密钥
- **DEPLOYMENT_BUCKET**: SAM部署使用的S3存储桶

### 可选环境变量
- **TABLE_PREFIX**: DynamoDB表前缀（默认: dev_）
- **JWT_SECRET**: JWT密钥（生产环境必须设置）
- **ENVIRONMENT**: 环境标识（dev/test/prod）

### 环境变量设置方法

#### 方法1: 使用开发环境变量文件（推荐）
项目根目录已提供 `.env.dev` 文件，包含所有必要的配置：

```bash
# 加载环境变量
source .env.dev

# 验证配置
echo $AWS_REGION
echo $TABLE_PREFIX
```

该文件包含：
- AWS基础配置（区域、部署桶等）
- DynamoDB配置（表前缀）
- 项目配置（JWT密钥等）
- 详细的使用说明

#### 方法2: 直接设置环境变量
```bash
export AWS_REGION="us-west-1"
export TABLE_PREFIX="dev_"
export DEPLOYMENT_BUCKET="fixguru-lambda-deployment-**********"
```

#### 方法3: 使用AWS CLI配置
```bash
# 配置AWS凭证
aws configure

# 设置默认区域
aws configure set region us-west-1
```

## 🚀 部署流程

### 推荐部署方式（一键部署）
```bash
# 1. 选择目标环境并加载配置
source .env.dev    # 或 .env.test 或 .env.prod

# 2. 一键部署
./scripts/deploy.sh dev    # 或 test 或 prod

# 3. 验证部署结果
# 部署完成后会显示API Gateway URL
```

### 传统部署方式（SAM命令）
```bash
# 1. 构建
sam build

# 2. 部署
sam deploy --stack-name ${PROJECT_NAME}-${ENVIRONMENT} \
           --region ${AWS_REGION} \
           --capabilities CAPABILITY_IAM \
           --parameter-overrides Environment=${ENVIRONMENT} \
           --s3-bucket ${DEPLOYMENT_BUCKET} \
           --no-confirm-changeset
```

### 部署后验证
```bash
# 1. 检查堆栈状态
aws cloudformation describe-stacks --stack-name ${STACK_NAME}

# 2. 测试API端点
curl -X GET "${API_GATEWAY_URL}/api/v1/health"

# 3. 验证DynamoDB连接
curl -X GET "${API_GATEWAY_URL}/api/v1/health/detailed"
```

## 🧹 资源清理

### 定期清理检查
```bash
# 1. 列出所有API Gateway
aws apigateway get-rest-apis --query 'items[].[name,id,createdDate]'

# 2. 列出所有Lambda函数
aws lambda list-functions --query 'Functions[].[FunctionName,Runtime,LastModified]'

# 3. 列出所有CloudFormation堆栈
aws cloudformation list-stacks --stack-status-filter CREATE_COMPLETE UPDATE_COMPLETE
```

### 安全删除流程
1. **备份数据**: 导出DynamoDB表数据
2. **确认依赖**: 检查资源间的依赖关系
3. **删除堆栈**: 使用CloudFormation删除
4. **验证清理**: 确认所有资源已删除

## ⚠️ 常见问题避免

### 1. 资源冲突
- **问题**: 多个堆栈创建同名资源
- **解决**: 使用唯一的资源命名规范
- **预防**: 部署前检查现有资源

### 2. 数据丢失
- **问题**: 删除堆栈时丢失DynamoDB数据
- **解决**: 设置DeletionPolicy: Retain
- **预防**: 定期备份重要数据

### 3. 权限问题
- **问题**: Lambda函数缺少必要权限
- **解决**: 在SAM模板中明确定义所需权限
- **预防**: 使用最小权限原则

### 4. 资源冲突避免策略
- **命名约定**: 使用项目名称+环境标识的命名模式
- **资源标签**: 为所有资源添加统一的标签
- **堆栈管理**: 每个环境使用独立的CloudFormation堆栈
- **定期清理**: 定期检查和清理未使用的资源

## 📋 当前项目配置

### 活跃资源
- **堆栈名称**: `fixguru-{environment}` (如: fixguru-dev)
- **API Gateway**: `fixguru-api-{environment}`
- **Lambda函数**: `fixguru-function-{environment}`
- **DynamoDB表**: `{environment}_User` (如: dev_User)
- **当前开发环境API端点**: `https://yjy4cjhi2e.execute-api.us-west-1.amazonaws.com/api`
- **API文档地址**: `https://yjy4cjhi2e.execute-api.us-west-1.amazonaws.com/api/doc.html`

### 基础设施详情
- **Lambda 函数名**: `fixguru-function-{environment}`
- **运行时**: Java 17
- **内存**: 1024MB
- **超时**: 30秒
- **区域**: us-west-1

### 技术栈版本
- **Spring Boot**: 3.2.5
- **AWS SDK**: 2.28.11 (最新版)
- **Knife4j**: 4.5.0 (最新版)
- **Lombok**: 1.18.34 (最新版)
- **Maven**: 3.9.x
- **Java**: 17 (LTS)

### DynamoDB配置
- **表名**: `{environment}_User`
- **计费模式**: 按需付费
- **GSI**: email-index, username-index

### 环境变量配置
```bash
export PROJECT_NAME="fixguru"
export ENVIRONMENT="dev"  # 或 test/prod
export AWS_REGION="us-west-1"
export DEPLOYMENT_BUCKET="fixguru-lambda-deployment-**********"
```

### Lambda环境变量
- `SPRING_PROFILES_ACTIVE`: dev/test/prod (根据部署环境)
- `ENVIRONMENT`: dev/test/prod
- `USER_TABLE_NAME`: {environment}_User
- `AWS_DYNAMODB_TABLE_PREFIX`: {environment}_

## 🚀 API 端点

### 健康检查
```bash
GET /api/v1/health
GET /api/v1/health/detailed
```

### 用户管理
```bash
# 创建用户
POST /api/v1/users

# 用户登录
POST /api/v1/users/login

# 根据用户ID获取用户信息
GET /api/v1/users/{userId}

# 根据用户名获取用户信息
GET /api/v1/users/username/{username}

# 根据邮箱获取用户信息
GET /api/v1/users/email/{email}

# 检查用户名是否存在
GET /api/v1/users/check/username/{username}

# 检查邮箱是否存在
GET /api/v1/users/check/email/{email}

# 用户状态管理
POST /api/v1/users/{userId}/activate    # 激活用户
POST /api/v1/users/{userId}/lock        # 锁定用户
POST /api/v1/users/{userId}/unlock      # 解锁用户
POST /api/v1/users/{userId}/disable     # 禁用用户
POST /api/v1/users/{userId}/enable      # 启用用户
```

## 🧪 测试示例

### 健康检查
```bash
# 基础健康检查
curl -X GET "${API_GATEWAY_URL}/api/v1/health"

# 详细健康检查
curl -X GET "${API_GATEWAY_URL}/api/v1/health/detailed"

# 示例响应
{
  "code": "200",
  "message": "系统运行正常",
  "data": {
    "application": "FixGuru",
    "profile": "dev",
    "version": "1.0.0",
    "status": "UP",
    "timestamp": "2025-07-28T11:32:59.283875667Z",
    "uptime": "0天 0小时 0分钟 7秒"
  },
  "timestamp": "2025-07-28T11:32:59.284431909Z",
  "success": true
}
```

### 创建用户
```bash
curl -X POST "${API_GATEWAY_URL}/api/v1/users" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "Test123456",
    "confirmPassword": "Test123456"
  }'
```

### 用户登录
```bash
curl -X POST "${API_GATEWAY_URL}/api/v1/users/login" \
  -H "Content-Type: application/json" \
  -d '{
    "account": "testuser",
    "password": "Test123456"
  }'
```

### API文档访问
```bash
# 访问Knife4j API文档
open "${API_GATEWAY_URL}/doc.html"

# 或使用curl检查文档页面
curl -I "${API_GATEWAY_URL}/doc.html"
```

> 💡 **提示**:
> - `${API_GATEWAY_URL}` 是部署完成后显示的API Gateway URL
> - 当前开发环境URL: `https://yjy4cjhi2e.execute-api.us-west-1.amazonaws.com/api`
> - API文档支持中文界面，无需认证即可访问

## 📊 性能指标与监控

### Lambda性能指标
- **冷启动时间**: ~7秒
- **热启动时间**: ~200ms
- **内存使用**: ~271MB
- **包大小**: ~46MB

### 性能优化建议
- **预热策略**: 使用CloudWatch Events定期调用Lambda函数
- **内存调优**: 根据实际使用情况调整内存分配
- **包大小优化**: 使用Maven Shade Plugin减少依赖包大小
- **连接池**: 配置DynamoDB连接池以提高性能

## 🛠️ 开发工具和脚本

### 本地开发环境

#### 启动本地环境（推荐）
```bash
# 1. 加载开发环境变量
source .env.dev

# 2. 在IDEA中启动Application.java
# 或使用命令行启动
mvn spring-boot:run

# 3. 访问API文档
http://localhost:8080/api/doc.html
```

### 🎯 部署策略（重要）

为了节省AWS CodePipeline额度，项目采用**手动触发**策略：

**📱 日常开发部署（推荐）**
- 使用：`./scripts/deploy.sh dev`
- 场景：功能开发、bug修复、日常调试
- 优势：快速部署，不消耗Pipeline额度
- 适用：90%的开发场景

**🚀 正式发布部署**
- 使用：`./scripts/aws/manage-pipeline.sh start prod`
- 场景：版本发布、生产部署、重要更新
- 优势：完整CI/CD流程、测试覆盖、审计记录
- 适用：正式发布、生产环境

**⚠️ 重要提醒**：git push 不会自动触发Pipeline，你可以安全地提交代码！

### AWS部署脚本

#### 手动部署（开发测试）
```bash
# 开发环境部署
source .env.dev && ./scripts/deploy.sh dev

# 测试环境部署
source .env.test && ./scripts/deploy.sh test

# 生产环境部署
source .env.prod && ./scripts/deploy.sh prod
```

#### CI/CD流水线部署（按需使用）
```bash
# 一次性创建CI/CD流水线基础设施
./scripts/deploy-pipeline.sh dev     # 开发环境
./scripts/deploy-pipeline.sh test    # 测试环境
./scripts/deploy-pipeline.sh prod    # 生产环境

# 手动触发Pipeline（节省额度）
./scripts/aws/manage-pipeline.sh start dev     # 手动触发开发环境
./scripts/aws/manage-pipeline.sh start prod    # 手动触发生产环境
./scripts/aws/manage-pipeline.sh status dev    # 查看状态
./scripts/aws/manage-pipeline.sh logs dev      # 查看日志

# 注意：git push 现在不会自动触发Pipeline，避免浪费额度
# 如需促发Pipeline，需要修改 pipeline-template.yaml 中的 PollForSourceChanges: true
```



### 实用工具脚本

#### 环境检查
```bash
# 项目编译检查
mvn clean compile

# 验证AWS连接（通过健康检查API）
curl http://localhost:8080/api/v1/health/detailed
```

#### AWS部署工具
```bash
# 环境管理器
./scripts/aws/env-manager.sh

# 手动测试API端点
curl -X GET "${API_GATEWAY_URL}/api/v1/health"
```

### 开发工作流

#### 本地开发流程
```bash
# 1. 加载开发环境变量
source .env.dev

# 2. 启动应用程序
mvn spring-boot:run
# 或在IDEA中直接启动Application.java

# 3. 进行开发和测试
# 访问: http://localhost:8080/api/doc.html
```

#### AWS部署流程
```bash
# 1. 选择目标环境并加载配置
source .env.dev    # 或 .env.test 或 .env.prod

# 2. 一键部署
./scripts/deploy.sh dev    # 或 test 或 prod

# 3. 验证部署结果
# 部署完成后会显示API Gateway URL
```

## 🔍 监控和日志

### CloudWatch 日志
- **日志组**: `/aws/lambda/fixguru-function-{environment}`
- **保留期**: 14天
- **日志级别**: INFO（生产环境）/ DEBUG（开发环境）

### 查看日志
```bash
# 开发环境日志
aws logs tail /aws/lambda/fixguru-function-dev --region us-west-1 --since 5m

# 测试环境日志
aws logs tail /aws/lambda/fixguru-function-test --region us-west-1 --since 5m

# 生产环境日志
aws logs tail /aws/lambda/fixguru-function-prod --region us-west-1 --since 5m

# 实时日志流
aws logs tail /aws/lambda/fixguru-function-dev --region us-west-1 --follow
```

### 监控指标
- **Lambda指标**: 调用次数、错误率、持续时间、并发执行
- **API Gateway指标**: 请求数、延迟、4XX/5XX错误
- **DynamoDB指标**: 读写容量、限流、错误率

## 🔄 持续改进与最佳实践

### 监控和告警
- **Lambda监控**: 设置错误率、持续时间、并发数告警
- **API Gateway监控**: 监控请求量、错误率、延迟
- **DynamoDB监控**: 设置读写容量、限流告警
- **成本监控**: 设置预算告警，监控AWS使用成本

### 自动化部署
- **CI/CD流水线**: 使用GitHub Actions或AWS CodePipeline
- **自动化测试**: 集成单元测试、集成测试
- **多环境部署**: 自动化dev→test→prod部署流程
- **回滚策略**: 实现快速回滚机制

### 安全最佳实践
- **IAM权限**: 遵循最小权限原则
- **API安全**: 实现JWT认证和授权
- **数据加密**: 启用DynamoDB加密和传输加密
- **访问日志**: 启用CloudTrail记录API调用

---

**最后更新**: 2025-07-28
**维护者**: Han
