version: 0.2

# FixGuru CodeBuild 构建规范
# 用于AWS CodePipeline自动化构建和部署

env:
  variables:
    # Maven配置
    MAVEN_OPTS: "-Dmaven.repo.local=/tmp/.m2/repository"
    # 项目配置
    PROJECT_NAME: "fixguru"
  parameter-store:
    # 从Parameter Store获取敏感配置
    JWT_SECRET: "/fixguru/jwt-secret"

phases:
  install:
    runtime-versions:
      java: corretto17
    commands:
      - echo "安装阶段开始..."
      - echo "Java版本:" && java -version
      - echo "Maven版本:" && mvn -version
      - echo "AWS CLI版本:" && aws --version
      - echo "SAM CLI版本:" && sam --version

  pre_build:
    commands:
      - echo "预构建阶段开始..."
      - echo "当前目录:" && pwd
      - echo "文件列表:" && ls -la
      - echo "检查环境变量..."
      - echo "ENVIRONMENT = $ENVIRONMENT"
      - echo "AWS_REGION = $AWS_REGION"
      - echo "PROJECT_NAME = $PROJECT_NAME"
      
      # 创建环境变量文件
      - echo "创建环境变量文件..."
      - |
        cat > .env.${ENVIRONMENT} << EOF
        export PROJECT_NAME=${PROJECT_NAME}
        export ENVIRONMENT=${ENVIRONMENT}
        export AWS_REGION=${AWS_REGION}
        export DEPLOYMENT_BUCKET=${DEPLOYMENT_BUCKET}
        export TABLE_PREFIX=${ENVIRONMENT}_
        export JWT_SECRET=${JWT_SECRET}
        export SPRING_PROFILES_ACTIVE=${ENVIRONMENT}
        EOF
      - echo "环境变量文件创建完成"
      - cat .env.${ENVIRONMENT}

  build:
    commands:
      - echo "构建阶段开始..."
      
      # Maven构建
      - echo "开始Maven构建..."
      - mvn clean compile -DskipTests -B -q
      - echo "Maven编译完成"
      
      # 运行测试（可选）
      - echo "运行单元测试..."
      - mvn test -B -q || echo "测试失败，但继续构建"
      
      # Maven打包
      - echo "开始Maven打包..."
      - mvn package -DskipTests -B -q
      - echo "Maven打包完成"
      
      # SAM构建
      - echo "开始SAM构建..."
      - sam build --use-container --debug
      - echo "SAM构建完成"
      
      # 验证构建结果
      - echo "验证构建结果..."
      - ls -la .aws-sam/build/
      - ls -la .aws-sam/build/FixGuruFunction/

  post_build:
    commands:
      - echo "后构建阶段开始..."
      
      # 创建部署包
      - echo "创建部署包..."
      - sam package --s3-bucket $DEPLOYMENT_BUCKET --output-template-file packaged-template.yaml
      - echo "部署包创建完成"
      
      # 验证模板
      - echo "验证CloudFormation模板..."
      - aws cloudformation validate-template --template-body file://packaged-template.yaml
      - echo "模板验证完成"
      
      - echo "构建阶段全部完成！"

artifacts:
  files:
    - packaged-template.yaml
    - .env.*
  name: FixGuruBuildArtifacts

cache:
  paths:
    - '/tmp/.m2/**/*'
    - 'target/**/*'
