package com.fixguru.exception;

import lombok.Getter;

/**
 * 系统异常类
 * 用于处理系统级别的异常
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Getter
public class SystemException extends RuntimeException {

    /**
     * 错误码
     */
    private final String errorCode;

    /**
     * 错误消息
     */
    private final String errorMessage;

    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param errorMessage 错误消息
     */
    public SystemException(String errorCode, String errorMessage) {
        super(errorMessage);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param errorMessage 错误消息
     * @param cause 原因异常
     */
    public SystemException(String errorCode, String errorMessage, Throwable cause) {
        super(errorMessage, cause);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    @Override
    public String toString() {
        return String.format("SystemException{errorCode='%s', errorMessage='%s'}", 
                errorCode, errorMessage);
    }
}
