package com.fixguru.exception;

import com.fixguru.common.ApiResponse;
import com.fixguru.enums.BizCodeEnum;
import com.fixguru.utils.TraceUtils;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;


import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理系统中的各种异常
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {


    /**
     * 处理业务异常
     */
    @ExceptionHandler(BizException.class)
    public ApiResponse<Void> handleBizException(BizException e, HttpServletResponse response) {
        if (e.isRetryable()) {
            log.warn("业务异常(可重试): {}", e.toString());
        } else {
            log.error("业务异常: {}", e.toString());
        }

        // 设置HTTP状态码 - 智能推断
        response.setStatus(determineHttpStatus(e.getErrorCode()).value());

        return ApiResponse.error(e.getErrorCode(), e.getErrorMessage()).withRequestId(TraceUtils.getRequestId());
    }

    /**
     * 处理系统异常
     */
    @ExceptionHandler(SystemException.class)
    public ApiResponse<Void> handleSystemException(SystemException e, HttpServletResponse response) {
        log.error("系统异常: {}", e.toString(), e);
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return ApiResponse.error(e.getErrorCode(), e.getErrorMessage());
    }

    /**
     * 处理参数验证异常（@RequestBody）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ApiResponse<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletResponse response) {
        log.warn("参数验证异常: {}", e.getMessage());
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), errorMessage);
    }

    /**
     * 处理参数绑定异常（@ModelAttribute）
     */
    @ExceptionHandler(BindException.class)
    public ApiResponse<Void> handleBindException(BindException e, HttpServletResponse response) {
        log.warn("参数绑定异常: {}", e.getMessage());
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), errorMessage);
    }

    /**
     * 处理约束违反异常（@Validated）
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ApiResponse<Void> handleConstraintViolationException(ConstraintViolationException e, HttpServletResponse response) {
        log.warn("约束违反异常: {}", e.getMessage());
        String errorMessage = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), errorMessage);
    }

    /**
     * 处理静态资源未找到异常（如favicon.ico）
     */
    @ExceptionHandler(NoResourceFoundException.class)
    public ApiResponse<Void> handleNoResourceFoundException(NoResourceFoundException e, HttpServletResponse response) {
        // 对于静态资源404，只记录DEBUG级别日志，避免日志噪音
        log.debug("静态资源未找到: {}", e.getResourcePath());
        response.setStatus(HttpStatus.NOT_FOUND.value());
        return ApiResponse.error(BizCodeEnum.INVALID_REQUEST.getCode(), "Resource not found");
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ApiResponse<Void> handleIllegalArgumentException(IllegalArgumentException e, HttpServletResponse response) {
        log.warn("非法参数异常: {}", e.getMessage());
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), e.getMessage());
    }

    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    public ApiResponse<Void> handleException(Exception e, HttpServletResponse response) {
        log.error("未知异常: {}", e.getMessage(), e);
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return ApiResponse.error(BizCodeEnum.SYSTEM_ERROR.getCode(), BizCodeEnum.SYSTEM_ERROR.getMessage())
                .withRequestId(TraceUtils.getRequestId());
    }

    /**
     * 根据错误码智能推断HTTP状态码
     * 零维护：基于枚举名称语义分析，新增错误码无需配置
     */
    private HttpStatus determineHttpStatus(String errorCode) {
        if (errorCode == null) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }

        // 查找对应的枚举进行语义分析
        BizCodeEnum bizCodeEnum = BizCodeEnum.fromCode(errorCode);
        if (bizCodeEnum == BizCodeEnum.SYSTEM_ERROR) {
            // 未找到对应枚举，使用分类默认值
            return getDefaultStatusByCategory(errorCode);
        }

        String enumName = bizCodeEnum.name();

        // 智能语义匹配 - 按优先级匹配关键词
        if (enumName.contains("NOT_FOUND")) return HttpStatus.NOT_FOUND;
        if (enumName.contains("ALREADY_EXISTS") || enumName.contains("TAKEN")) return HttpStatus.CONFLICT;
        if (enumName.contains("FORBIDDEN")) return HttpStatus.FORBIDDEN;
        if (enumName.contains("RATE_LIMIT") || enumName.contains("LIMIT_EXCEEDED")) return HttpStatus.TOO_MANY_REQUESTS;
        if (enumName.contains("TOO_LARGE")) return HttpStatus.PAYLOAD_TOO_LARGE;
        if (enumName.contains("INVALID_FILE_TYPE") || enumName.contains("UNSUPPORTED")) return HttpStatus.UNSUPPORTED_MEDIA_TYPE;
        if (enumName.contains("INVALID") || enumName.contains("REQUIRED") ||
            enumName.contains("MISMATCH") || enumName.contains("WEAK")) return HttpStatus.BAD_REQUEST;

        // 使用分类默认值
        return getDefaultStatusByCategory(errorCode);
    }

    /**
     * 根据错误码分类返回默认HTTP状态码
     */
    private HttpStatus getDefaultStatusByCategory(String errorCode) {
        if (errorCode.isEmpty()) return HttpStatus.INTERNAL_SERVER_ERROR;

        switch (errorCode.charAt(0)) {
            case '0': return HttpStatus.OK;                    // 成功
            case '1': return HttpStatus.INTERNAL_SERVER_ERROR; // 系统错误
            case '2': return HttpStatus.BAD_REQUEST;           // 用户错误
            case '3': return HttpStatus.UNAUTHORIZED;          // 认证错误
            case '4': return HttpStatus.INTERNAL_SERVER_ERROR; // 文件错误
            case '5': return HttpStatus.BAD_REQUEST;           // 验证错误
            case '6': return HttpStatus.INTERNAL_SERVER_ERROR; // 数据库错误
            default:  return HttpStatus.INTERNAL_SERVER_ERROR; // 未知错误
        }
    }
}
