package com.fixguru.exception;

import com.fixguru.common.ApiResponse;
import com.fixguru.enums.BizCodeEnum;
import com.fixguru.utils.TraceUtils;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理系统中的各种异常
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {


    /**
     * 处理业务异常
     */
    @ExceptionHandler(BizException.class)
    public ResponseEntity<ApiResponse<Void>> handleBizException(BizException e) {
        if (e.isRetryable()) {
            log.warn("业务异常(可重试): {}", e.toString());
        } else {
            log.error("业务异常: {}", e.toString());
        }

        ApiResponse<Void> response = ApiResponse.error(e.getErrorCode(), e.getErrorMessage()).withRequestId(TraceUtils.getRequestId());

        // 根据错误码确定HTTP状态码
        HttpStatus status = determineHttpStatus(e.getErrorCode());
        return ResponseEntity.status(status).body(response);
    }

    /**
     * 处理系统异常
     */
    @ExceptionHandler(SystemException.class)
    public ResponseEntity<ApiResponse<Void>> handleSystemException(SystemException e) {
        log.error("系统异常: {}", e.toString(), e);
        ApiResponse<Void> response = ApiResponse.error(e.getErrorCode(), e.getErrorMessage());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理参数验证异常（@RequestBody）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Void>> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.warn("参数验证异常: {}", e.getMessage());
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        ApiResponse<Void> response = ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), errorMessage);
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理参数绑定异常（@ModelAttribute）
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<ApiResponse<Void>> handleBindException(BindException e) {
        log.warn("参数绑定异常: {}", e.getMessage());
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        ApiResponse<Void> response = ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), errorMessage);
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理约束违反异常（@Validated）
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ApiResponse<Void>> handleConstraintViolationException(ConstraintViolationException e) {
        log.warn("约束违反异常: {}", e.getMessage());
        String errorMessage = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        ApiResponse<Void> response = ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), errorMessage);
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理静态资源未找到异常（如favicon.ico）
     */
    @ExceptionHandler(NoResourceFoundException.class)
    public ResponseEntity<ApiResponse<Void>> handleNoResourceFoundException(NoResourceFoundException e) {
        // 对于静态资源404，只记录DEBUG级别日志，避免日志噪音
        log.debug("静态资源未找到: {}", e.getResourcePath());
        ApiResponse<Void> response = ApiResponse.error(BizCodeEnum.INVALID_REQUEST.getCode(), "Resource not found");
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponse<Void>> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("非法参数异常: {}", e.getMessage());
        ApiResponse<Void> response = ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), e.getMessage());
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Void>> handleException(Exception e) {
        log.error("未知异常: {}", e.getMessage(), e);
        ApiResponse<Void> response = ApiResponse.error(BizCodeEnum.SYSTEM_ERROR.getCode(), BizCodeEnum.SYSTEM_ERROR.getMessage())
                .withRequestId(TraceUtils.getRequestId());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 根据错误码确定HTTP状态码
     */
    private HttpStatus determineHttpStatus(String errorCode) {
        if (errorCode == null) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }

        // 系统错误
        if (errorCode.startsWith("1")) {
            if (BizCodeEnum.INVALID_PARAMETER.getCode().equals(errorCode)) {
                return HttpStatus.BAD_REQUEST;
            }
            if (BizCodeEnum.RATE_LIMIT_EXCEEDED.getCode().equals(errorCode)) {
                return HttpStatus.TOO_MANY_REQUESTS;
            }
            return HttpStatus.INTERNAL_SERVER_ERROR;
        // 用户错误
        } else if (errorCode.startsWith("2")) {
            if (BizCodeEnum.USER_NOT_FOUND.getCode().equals(errorCode)) {
                return HttpStatus.NOT_FOUND;
            }
            if (BizCodeEnum.EMAIL_ALREADY_EXISTS.getCode().equals(errorCode)) {
                return HttpStatus.CONFLICT;
            }
            return HttpStatus.BAD_REQUEST;
        // 认证错误
        } else if (errorCode.startsWith("3")) {
            if (BizCodeEnum.UNAUTHORIZED.getCode().equals(errorCode) ||
                    BizCodeEnum.TOKEN_INVALID.getCode().equals(errorCode) ||
                    BizCodeEnum.TOKEN_EXPIRED.getCode().equals(errorCode)) {
                return HttpStatus.UNAUTHORIZED;
            }
            if (BizCodeEnum.FORBIDDEN.getCode().equals(errorCode)) {
                return HttpStatus.FORBIDDEN;
            }
            return HttpStatus.UNAUTHORIZED;
        // 文件错误
        } else if (errorCode.startsWith("4")) {
            if (BizCodeEnum.FILE_NOT_FOUND.getCode().equals(errorCode)) {
                return HttpStatus.NOT_FOUND;
            }
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }

        return HttpStatus.INTERNAL_SERVER_ERROR;
    }
}
