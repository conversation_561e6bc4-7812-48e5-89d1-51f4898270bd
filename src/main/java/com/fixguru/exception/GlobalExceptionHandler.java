package com.fixguru.exception;

import com.fixguru.common.ApiResponse;
import com.fixguru.enums.BizCodeEnum;
import com.fixguru.utils.TraceUtils;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;


import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理系统中的各种异常
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {


    /**
     * 处理业务异常
     */
    @ExceptionHandler(BizException.class)
    public ApiResponse<Void> handleBizException(BizException e, HttpServletResponse response) {
        if (e.isRetryable()) {
            log.warn("业务异常(可重试): {}", e.toString());
        } else {
            log.error("业务异常: {}", e.toString());
        }

        // 设置HTTP状态码 - 简单映射
        response.setStatus(getHttpStatus(e.getErrorCode()).value());

        return ApiResponse.error(e.getErrorCode(), e.getErrorMessage()).withRequestId(TraceUtils.getRequestId());
    }

    /**
     * 处理系统异常
     */
    @ExceptionHandler(SystemException.class)
    public ApiResponse<Void> handleSystemException(SystemException e, HttpServletResponse response) {
        log.error("系统异常: {}", e.toString(), e);
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return ApiResponse.error(e.getErrorCode(), e.getErrorMessage());
    }

    /**
     * 处理参数验证异常（@RequestBody）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ApiResponse<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletResponse response) {
        log.warn("参数验证异常: {}", e.getMessage());
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), errorMessage);
    }

    /**
     * 处理参数绑定异常（@ModelAttribute）
     */
    @ExceptionHandler(BindException.class)
    public ApiResponse<Void> handleBindException(BindException e, HttpServletResponse response) {
        log.warn("参数绑定异常: {}", e.getMessage());
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), errorMessage);
    }

    /**
     * 处理约束违反异常（@Validated）
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ApiResponse<Void> handleConstraintViolationException(ConstraintViolationException e, HttpServletResponse response) {
        log.warn("约束违反异常: {}", e.getMessage());
        String errorMessage = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), errorMessage);
    }

    /**
     * 处理静态资源未找到异常（如favicon.ico）
     */
    @ExceptionHandler(NoResourceFoundException.class)
    public ApiResponse<Void> handleNoResourceFoundException(NoResourceFoundException e, HttpServletResponse response) {
        // 对于静态资源404，只记录DEBUG级别日志，避免日志噪音
        log.debug("静态资源未找到: {}", e.getResourcePath());
        response.setStatus(HttpStatus.NOT_FOUND.value());
        return ApiResponse.error(BizCodeEnum.INVALID_REQUEST.getCode(), "Resource not found");
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ApiResponse<Void> handleIllegalArgumentException(IllegalArgumentException e, HttpServletResponse response) {
        log.warn("非法参数异常: {}", e.getMessage());
        response.setStatus(HttpStatus.BAD_REQUEST.value());
        return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), e.getMessage());
    }

    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    public ApiResponse<Void> handleException(Exception e, HttpServletResponse response) {
        log.error("未知异常: {}", e.getMessage(), e);
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        return ApiResponse.error(BizCodeEnum.SYSTEM_ERROR.getCode(), BizCodeEnum.SYSTEM_ERROR.getMessage())
                .withRequestId(TraceUtils.getRequestId());
    }

    /**
     * 获取HTTP状态码 - 简单直接的映射
     */
    private HttpStatus getHttpStatus(String errorCode) {
        if (errorCode == null) return HttpStatus.INTERNAL_SERVER_ERROR;

        // 直接匹配常见错误码
        switch (errorCode) {
            // 404错误
            case "2001": case "4001": case "6005": case "1006": return HttpStatus.NOT_FOUND;
            // 409冲突
            case "2010": case "2011": case "2013": return HttpStatus.CONFLICT;
            // 403禁止
            case "3006": return HttpStatus.FORBIDDEN;
            // 429限流
            case "1002": return HttpStatus.TOO_MANY_REQUESTS;
            // 413文件过大
            case "4002": return HttpStatus.PAYLOAD_TOO_LARGE;
            // 415不支持的媒体类型
            case "4003": return HttpStatus.UNSUPPORTED_MEDIA_TYPE;
            // 400参数错误
            case "1004": case "1003": return HttpStatus.BAD_REQUEST;
            default:
                // 按分类返回默认状态码
                char firstChar = errorCode.charAt(0);
                switch (firstChar) {
                    case '0': return HttpStatus.OK;
                    case '1': return HttpStatus.INTERNAL_SERVER_ERROR;
                    case '2': return HttpStatus.BAD_REQUEST;
                    case '3': return HttpStatus.UNAUTHORIZED;
                    case '4': return HttpStatus.INTERNAL_SERVER_ERROR;
                    case '5': return HttpStatus.BAD_REQUEST;
                    case '6': return HttpStatus.INTERNAL_SERVER_ERROR;
                    default: return HttpStatus.INTERNAL_SERVER_ERROR;
                }
        }
    }
}
