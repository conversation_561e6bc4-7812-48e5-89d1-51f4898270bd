package com.fixguru.exception;

import com.fixguru.common.ApiResponse;
import com.fixguru.enums.BizCodeEnum;
import com.fixguru.utils.TraceUtils;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理系统中的各种异常
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BizException.class)
    public ResponseEntity<ApiResponse<Void>> handleBizException(BizException e) {
        if (e.isRetryable()) {
            log.warn("业务异常(可重试): {}", e.toString());
        } else {
            log.error("业务异常: {}", e.toString());
        }

        ApiResponse<Void> response = ApiResponse.error(e.getErrorCode(), e.getErrorMessage())
                .withRequestId(TraceUtils.getRequestId());

        // 根据错误码确定HTTP状态码
        HttpStatus status = determineHttpStatus(e.getErrorCode());
        return ResponseEntity.status(status).body(response);
    }

    /**
     * 处理系统异常
     */
    @ExceptionHandler(SystemException.class)
    public ResponseEntity<ApiResponse<Void>> handleSystemException(SystemException e) {
        log.error("系统异常: {}", e.toString(), e);
        ApiResponse<Void> response = ApiResponse.error(e.getErrorCode(), e.getErrorMessage());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理参数相关异常（统一处理所有参数异常）
     */
    @ExceptionHandler({
            MethodArgumentNotValidException.class,
            BindException.class,
            ConstraintViolationException.class,
            IllegalArgumentException.class,
            NoResourceFoundException.class
    })
    public ResponseEntity<ApiResponse<Void>> handleParameterException(Exception e) {
        String errorMessage;

        if (e instanceof MethodArgumentNotValidException validException) {
            log.warn("参数验证异常: {}", validException.getMessage());
            BindingResult bindingResult = validException.getBindingResult();
            errorMessage = bindingResult.getFieldErrors().stream()
                    .map(FieldError::getDefaultMessage)
                    .collect(Collectors.joining(", "));
        } else if (e instanceof BindException bindException) {
            log.warn("参数绑定异常: {}", bindException.getMessage());
            BindingResult bindingResult = bindException.getBindingResult();
            errorMessage = bindingResult.getFieldErrors().stream()
                    .map(FieldError::getDefaultMessage)
                    .collect(Collectors.joining(", "));
        } else if (e instanceof ConstraintViolationException constraintException) {
            log.warn("约束违反异常: {}", constraintException.getMessage());
            errorMessage = constraintException.getConstraintViolations().stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(", "));
        } else if (e instanceof NoResourceFoundException resourceException) {
            log.debug("静态资源未找到: {}", resourceException.getResourcePath());
            errorMessage = "Resource not found";
        } else {
            log.warn("非法参数异常: {}", e.getMessage());
            errorMessage = e.getMessage();
        }

        ApiResponse<Void> response = ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), errorMessage);
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * 根据错误码确定HTTP状态码
     */
    private HttpStatus determineHttpStatus(String errorCode) {
        if (errorCode == null) return HttpStatus.INTERNAL_SERVER_ERROR;

        // 直接匹配常见错误码
        switch (errorCode) {
            // 404错误
            case "2001": case "4001": case "6005": case "1006": return HttpStatus.NOT_FOUND;
            // 409冲突
            case "2010": case "2011": case "2013": return HttpStatus.CONFLICT;
            // 403禁止
            case "3006": return HttpStatus.FORBIDDEN;
            // 429限流
            case "1002": return HttpStatus.TOO_MANY_REQUESTS;
            // 413文件过大
            case "4002": return HttpStatus.PAYLOAD_TOO_LARGE;
            // 415不支持的媒体类型
            case "4003": return HttpStatus.UNSUPPORTED_MEDIA_TYPE;
            // 400参数错误
            case "1004": case "1003": return HttpStatus.BAD_REQUEST;
            default:
                // 按分类返回默认状态码
                char firstChar = errorCode.charAt(0);
                switch (firstChar) {
                    case '0': return HttpStatus.OK;
                    case '1': return HttpStatus.INTERNAL_SERVER_ERROR;
                    case '2': return HttpStatus.BAD_REQUEST;
                    case '3': return HttpStatus.UNAUTHORIZED;
                    case '4': return HttpStatus.INTERNAL_SERVER_ERROR;
                    case '5': return HttpStatus.BAD_REQUEST;
                    case '6': return HttpStatus.INTERNAL_SERVER_ERROR;
                    default: return HttpStatus.INTERNAL_SERVER_ERROR;
                }
        }
    }
}
