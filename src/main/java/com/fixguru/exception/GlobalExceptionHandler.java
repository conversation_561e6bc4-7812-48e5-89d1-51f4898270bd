package com.fixguru.exception;

import com.fixguru.common.ApiResponse;
import com.fixguru.enums.BizCodeEnum;
import com.fixguru.utils.TraceUtils;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理系统中的各种异常
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {


    /**
     * 统一异常处理
     */
    @ExceptionHandler(Throwable.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleException(Throwable e) {
        if (e instanceof BizException) {
            BizException bizException = (BizException) e;
            if (bizException.isRetryable()) {
                log.warn("业务异常(可重试): {}", bizException.toString());
            } else {
                log.error("业务异常: {}", bizException.toString());
            }
            return ApiResponse.error(bizException.getErrorCode(), bizException.getErrorMessage())
                    .withRequestId(TraceUtils.getRequestId());
        } else if (e instanceof SystemException) {
            SystemException systemException = (SystemException) e;
            log.error("系统异常: {}", systemException.toString(), e);
            return ApiResponse.error(systemException.getErrorCode(), systemException.getErrorMessage());
        } else if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException validException = (MethodArgumentNotValidException) e;
            log.warn("参数验证异常: {}", validException.getMessage());
            BindingResult bindingResult = validException.getBindingResult();
            if (bindingResult.hasErrors()) {
                String errorMessage = bindingResult.getFieldErrors().stream()
                        .map(FieldError::getDefaultMessage)
                        .collect(Collectors.joining(", "));
                return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), errorMessage);
            }
        } else if (e instanceof BindException) {
            BindException bindException = (BindException) e;
            log.warn("参数绑定异常: {}", bindException.getMessage());
            BindingResult bindingResult = bindException.getBindingResult();
            if (bindingResult.hasErrors()) {
                String errorMessage = bindingResult.getFieldErrors().stream()
                        .map(FieldError::getDefaultMessage)
                        .collect(Collectors.joining(", "));
                return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), errorMessage);
            }
        } else if (e instanceof ConstraintViolationException) {
            ConstraintViolationException constraintException = (ConstraintViolationException) e;
            log.warn("约束违反异常: {}", constraintException.getMessage());
            String errorMessage = constraintException.getConstraintViolations().stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(", "));
            return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), errorMessage);
        } else if (e instanceof NoResourceFoundException) {
            NoResourceFoundException resourceException = (NoResourceFoundException) e;
            log.debug("静态资源未找到: {}", resourceException.getResourcePath());
            return ApiResponse.error(BizCodeEnum.INVALID_REQUEST.getCode(), "Resource not found");
        } else if (e instanceof IllegalArgumentException) {
            log.warn("非法参数异常: {}", e.getMessage());
            return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), e.getMessage());
        }

        log.error("未知异常: {}", e.getMessage(), e);
        return ApiResponse.error(BizCodeEnum.SYSTEM_ERROR.getCode(), BizCodeEnum.SYSTEM_ERROR.getMessage())
                .withRequestId(TraceUtils.getRequestId());
    }
}
