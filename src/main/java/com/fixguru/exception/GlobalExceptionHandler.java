package com.fixguru.exception;

import com.fixguru.common.ApiResponse;
import com.fixguru.enums.BizCodeEnum;
import com.fixguru.utils.TraceUtils;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理系统中的各种异常
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {


    /**
     * 统一异常处理
     */
    @ExceptionHandler(Throwable.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleException(Throwable e) {

        // 业务异常
        if (e instanceof BizException bizException) {
            return handleBizException(bizException);
        }

        // 系统异常
        if (e instanceof SystemException systemException) {
            return handleSystemException(systemException);
        }

        // 参数验证异常
        if (e instanceof MethodArgumentNotValidException validException) {
            return handleValidationException(validException);
        }

        // 参数绑定异常
        if (e instanceof BindException bindException) {
            return handleBindException(bindException);
        }

        // 约束违反异常
        if (e instanceof ConstraintViolationException constraintException) {
            return handleConstraintException(constraintException);
        }

        // 静态资源未找到异常
        if (e instanceof NoResourceFoundException resourceException) {
            return handleResourceNotFoundException(resourceException);
        }

        // 非法参数异常
        if (e instanceof IllegalArgumentException illegalException) {
            return handleIllegalArgumentException(illegalException);
        }

        // 未知异常
        return handleUnknownException(e);
    }

    /**
     * 处理业务异常
     */
    private ApiResponse<Void> handleBizException(BizException e) {
        if (e.isRetryable()) {
            log.warn("业务异常(可重试): {}", e.toString());
        } else {
            log.error("业务异常: {}", e.toString());
        }

        return ApiResponse.error(e.getErrorCode(), e.getErrorMessage())
                .withRequestId(TraceUtils.getRequestId());
    }

    /**
     * 处理系统异常
     */
    private ApiResponse<Void> handleSystemException(SystemException e) {
        log.error("系统异常: {}", e.toString(), e);
        return ApiResponse.error(e.getErrorCode(), e.getErrorMessage());
    }

    /**
     * 处理参数验证异常
     */
    private ApiResponse<Void> handleValidationException(MethodArgumentNotValidException e) {
        log.warn("参数验证异常: {}", e.getMessage());

        BindingResult bindingResult = e.getBindingResult();
        if (bindingResult.hasErrors()) {
            String errorMessage = bindingResult.getFieldErrors().stream()
                    .map(FieldError::getDefaultMessage)
                    .collect(Collectors.joining(", "));
            return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), errorMessage);
        }

        return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), "参数验证失败");
    }

    /**
     * 处理参数绑定异常
     */
    private ApiResponse<Void> handleBindException(BindException e) {
        log.warn("参数绑定异常: {}", e.getMessage());

        BindingResult bindingResult = e.getBindingResult();
        if (bindingResult.hasErrors()) {
            String errorMessage = bindingResult.getFieldErrors().stream()
                    .map(FieldError::getDefaultMessage)
                    .collect(Collectors.joining(", "));
            return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), errorMessage);
        }

        return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), "参数绑定失败");
    }

    /**
     * 处理约束违反异常
     */
    private ApiResponse<Void> handleConstraintException(ConstraintViolationException e) {
        log.warn("约束违反异常: {}", e.getMessage());

        String errorMessage = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));

        return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), errorMessage);
    }

    /**
     * 处理静态资源未找到异常
     */
    private ApiResponse<Void> handleResourceNotFoundException(NoResourceFoundException e) {
        log.debug("静态资源未找到: {}", e.getResourcePath());
        return ApiResponse.error(BizCodeEnum.INVALID_REQUEST.getCode(), "Resource not found");
    }

    /**
     * 处理非法参数异常
     */
    private ApiResponse<Void> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("非法参数异常: {}", e.getMessage());
        return ApiResponse.error(BizCodeEnum.INVALID_PARAMETER.getCode(), e.getMessage());
    }

    /**
     * 处理未知异常
     */
    private ApiResponse<Void> handleUnknownException(Throwable e) {
        log.error("未知异常: {}", e.getMessage(), e);
        return ApiResponse.error(BizCodeEnum.SYSTEM_ERROR.getCode(), BizCodeEnum.SYSTEM_ERROR.getMessage())
                .withRequestId(TraceUtils.getRequestId());
    }
}
