package com.fixguru.exception;

import com.fixguru.enums.BizCodeEnum;
import lombok.Getter;

/**
 * 业务异常类
 * 企业级业务逻辑异常处理
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Getter
public class BizException extends RuntimeException {

    /**
     * 错误码
     */
    private final String errorCode;

    /**
     * 错误消息
     */
    private final String errorMessage;

    /**
     * 错误详情
     */
    private final Object errorDetails;

    /**
     * 是否可重试
     */
    private final boolean retryable;

    public BizException(String errorCode, String errorMessage) {
        this(errorCode, errorMessage, null, false, HttpStatus.BAD_REQUEST);
    }

    public BizException(String errorCode, String errorMessage, HttpStatus httpStatus) {
        this(errorCode, errorMessage, null, false, httpStatus);
    }

    public BizException(String errorCode, String errorMessage, Object errorDetails) {
        this(errorCode, errorMessage, errorDetails, false, HttpStatus.BAD_REQUEST);
    }

    public BizException(String errorCode, String errorMessage, boolean retryable) {
        this(errorCode, errorMessage, null, retryable, HttpStatus.BAD_REQUEST);
    }

    public BizException(String errorCode, String errorMessage, Object errorDetails, boolean retryable, HttpStatus httpStatus) {
        super(errorMessage);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.errorDetails = errorDetails;
        this.retryable = retryable;
        this.httpStatus = httpStatus;
    }

    public BizException(String errorCode, String errorMessage, Throwable cause) {
        this(errorCode, errorMessage, null, false, cause);
    }

    public BizException(String errorCode, String errorMessage, Object errorDetails, boolean retryable, Throwable cause) {
        super(errorMessage, cause);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.errorDetails = errorDetails;
        this.retryable = retryable;
    }

    // ========================================
    // BizCodeEnum枚举构造方法
    // ========================================

    public BizException(BizCodeEnum bizCodeEnum) {
        this(bizCodeEnum.getCode(), bizCodeEnum.getMessage(), null, false);
    }

    public BizException(BizCodeEnum bizCodeEnum, Object errorDetails) {
        this(bizCodeEnum.getCode(), bizCodeEnum.getMessage(), errorDetails, false);
    }

    public BizException(BizCodeEnum bizCodeEnum, boolean retryable) {
        this(bizCodeEnum.getCode(), bizCodeEnum.getMessage(), null, retryable);
    }

    public BizException(BizCodeEnum bizCodeEnum, Object errorDetails, boolean retryable) {
        this(bizCodeEnum.getCode(), bizCodeEnum.getMessage(), errorDetails, retryable);
    }

    public BizException(BizCodeEnum bizCodeEnum, Throwable cause) {
        this(bizCodeEnum.getCode(), bizCodeEnum.getMessage(), null, false, cause);
    }

    // ========================================
    // 静态工厂方法
    // ========================================

    /**
     * 创建业务异常
     */
    public static BizException of(String errorCode, String errorMessage) {
        return new BizException(errorCode, errorMessage);
    }

    /**
     * 创建业务异常（带详情）
     */
    public static BizException of(String errorCode, String errorMessage, Object errorDetails) {
        return new BizException(errorCode, errorMessage, errorDetails);
    }

    /**
     * 创建可重试异常
     */
    public static BizException retryable(String errorCode, String errorMessage) {
        return new BizException(errorCode, errorMessage, true);
    }

    /**
     * 创建BizCodeEnum枚举异常
     */
    public static BizException of(BizCodeEnum bizCodeEnum) {
        return new BizException(bizCodeEnum);
    }

    public static BizException of(BizCodeEnum bizCodeEnum, Object errorDetails) {
        return new BizException(bizCodeEnum, errorDetails);
    }

    /**
     * 创建参数验证异常
     */
    public static BizException validation(String errorMessage) {
        return new BizException(BizCodeEnum.INVALID_PARAMETER.getCode(), errorMessage);
    }

    /**
     * 创建资源不存在异常
     */
    public static BizException notFound(String resourceType, String resourceId) {
        return new BizException(BizCodeEnum.RESOURCE_NOT_FOUND,
                String.format("%s not found: %s", resourceType, resourceId));
    }

    /**
     * 创建权限不足异常
     */
    public static BizException forbidden(String operation) {
        return new BizException(BizCodeEnum.FORBIDDEN,
                String.format("Access denied for operation: %s", operation));
    }

    /**
     * 创建认证失败异常
     */
    public static BizException unauthorized(String reason) {
        return new BizException(BizCodeEnum.UNAUTHORIZED,
                String.format("Authentication failed: %s", reason));
    }

    /**
     * 创建限流异常
     */
    public static BizException rateLimited(String operation) {
        return new BizException(BizCodeEnum.RATE_LIMIT_EXCEEDED,
                String.format("Rate limit exceeded for operation: %s", operation));
    }

    @Override
    public String toString() {
        return String.format("BizException{errorCode='%s', errorMessage='%s', retryable=%s}",
                errorCode, errorMessage, retryable);
    }
}
