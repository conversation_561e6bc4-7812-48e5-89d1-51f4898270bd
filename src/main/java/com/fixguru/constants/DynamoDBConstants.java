package com.fixguru.constants;

/**
 * DynamoDB相关常量
 * 统一管理DynamoDB配置中的常量值
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class DynamoDBConstants {

    /**
     * API调用总超时（秒）
     */
    public static final int API_CALL_TIMEOUT_SECONDS = 30;

    /**
     * API单次调用超时（秒）
     */
    public static final int API_CALL_ATTEMPT_TIMEOUT_SECONDS = 10;

    /**
     * 重试最大次数
     */
    public static final int RETRY_MAX_ATTEMPTS = 3;

    /**
     * 性能监控阈值，考虑网络延迟调整为2秒
     */
    public static final long GSI_QUERY_SLOW_THRESHOLD_MS = 2000;

    /**
     * 连接池最大连接数
     */
    public static final int MAX_CONNECTIONS = 50;

    /**
     * 连接超时时5秒
     */
    public static final int CONNECTION_TIMEOUT_MS = 5000;

    /**
     * Socket超时8秒
     */
    public static final int SOCKET_TIMEOUT_MS = 8000;

    /**
     * 连接最大空闲时间（分钟）
     */
    public static final int CONNECTION_MAX_IDLE_MINUTES = 5;

    /**
     * 连接最大生存时间（分钟）
     */
    public static final int CONNECTION_TIME_TO_LIVE_MINUTES = 10;

    /**
     * GSI索引名称
     */
    public static final String USERNAME_GSI_INDEX = "username-index";
    public static final String EMAIL_GSI_INDEX = "email-index";

    /**
     * 字段名称常量（用于日志记录）
     */
    public static final String USERNAME_FIELD = "username";
    public static final String EMAIL_FIELD = "email";


    private DynamoDBConstants() {
        // 工具类，禁止实例化
    }
}