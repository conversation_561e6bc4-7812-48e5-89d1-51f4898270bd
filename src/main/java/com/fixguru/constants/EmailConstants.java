package com.fixguru.constants;

/**
 * 邮件相关常量
 * 统一管理邮件配置中的常量值
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class EmailConstants {

    /**
     * SMTP连接超时5秒
     */
    public static final String SMTP_CONNECTION_TIMEOUT = "5000";

    /**
     * SMTP读取超时8秒
     */
    public static final String SMTP_READ_TIMEOUT = "8000";

    /**
     * SMTP写入超时5秒
     */
    public static final String SMTP_WRITE_TIMEOUT = "5000";

    /**
     * SMTP连接池大小
     */
    public static final String SMTP_CONNECTION_POOL_SIZE = "10";

    /**
     * SMTP连接池超时 5 分钟
     */
    public static final String SMTP_CONNECTION_POOL_TIMEOUT = "300000";

    /**
     * 邮件线程池核心线程数
     */
    public static final int EMAIL_THREAD_CORE_POOL_SIZE = 2;

    /**
     * 邮件线程池最大线程数
     */
    public static final int EMAIL_THREAD_MAX_POOL_SIZE = 5;

    /**
     * 邮件线程池队列容量
     */
    public static final int EMAIL_THREAD_QUEUE_CAPACITY = 100;

    /**
     * 邮件线程空闲时间（秒）
     */
    public static final int EMAIL_THREAD_KEEP_ALIVE_SECONDS = 60;

    /**
     * 线程池关闭等待时间（秒）
     */
    public static final int EMAIL_THREAD_SHUTDOWN_TIMEOUT_SECONDS = 30;


    private EmailConstants() {
        // 工具类，禁止实例化
    }
}
