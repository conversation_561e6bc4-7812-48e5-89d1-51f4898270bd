package com.fixguru.aws.dynamodb;

import com.fixguru.constants.SystemConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.AttributeDefinition;
import software.amazon.awssdk.services.dynamodb.model.BillingMode;
import software.amazon.awssdk.services.dynamodb.model.CreateTableRequest;
import software.amazon.awssdk.services.dynamodb.model.CreateTableResponse;
import software.amazon.awssdk.services.dynamodb.model.DescribeTableRequest;
import software.amazon.awssdk.services.dynamodb.model.DescribeTableResponse;
import software.amazon.awssdk.services.dynamodb.model.GlobalSecondaryIndex;
import software.amazon.awssdk.services.dynamodb.model.IndexStatus;
import software.amazon.awssdk.services.dynamodb.model.KeySchemaElement;
import software.amazon.awssdk.services.dynamodb.model.KeyType;
import software.amazon.awssdk.services.dynamodb.model.Projection;
import software.amazon.awssdk.services.dynamodb.model.ProjectionType;
import software.amazon.awssdk.services.dynamodb.model.ResourceInUseException;
import software.amazon.awssdk.services.dynamodb.model.ResourceNotFoundException;
import software.amazon.awssdk.services.dynamodb.model.ScalarAttributeType;
import software.amazon.awssdk.services.dynamodb.model.TableDescription;
import software.amazon.awssdk.services.dynamodb.model.TableStatus;

import java.util.Arrays;
import java.util.List;

/**
 * GSI表管理器
 * 负责全局二级索引表的创建和管理
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-27
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Profile("local") // 仅在本地环境运行
public class GSITableManager implements CommandLineRunner {

    private final DynamoDbClient dynamoDbClient;
    private final String tablePrefix;

    @Override
    public void run(String... args) {
        log.info("开始初始化带GSI的DynamoDB表...");
        
        try {
            createUserTableWithGSI();
            log.info("带GSI的DynamoDB表初始化完成");
        } catch (Exception e) {
            log.error("带GSI的DynamoDB表初始化失败", e);
        }
    }

    /**
     * 创建带有GSI的用户表
     */
    private void createUserTableWithGSI() {
        String tableName = tablePrefix + SystemConstants.TABLE_USER;
        
        if (tableExists(tableName)) {
            log.info("表已存在，跳过创建: {}", tableName);
            return;
        }

        log.info("创建带GSI的用户表: {}", tableName);

        try {
            CreateTableRequest request = CreateTableRequest.builder()
                    .tableName(tableName)
                    // 主键定义
                    .keySchema(
                            KeySchemaElement.builder()
                                    .attributeName("userId")
                                    .keyType(KeyType.HASH)
                                    .build()
                    )
                    // 属性定义
                    .attributeDefinitions(
                            AttributeDefinition.builder()
                                    .attributeName("userId")
                                    .attributeType(ScalarAttributeType.S)
                                    .build(),
                            AttributeDefinition.builder()
                                    .attributeName("username")
                                    .attributeType(ScalarAttributeType.S)
                                    .build(),
                            AttributeDefinition.builder()
                                    .attributeName("email")
                                    .attributeType(ScalarAttributeType.S)
                                    .build()
                    )
                    // 全局二级索引定义
                    .globalSecondaryIndexes(
                            // 用户名索引
                            GlobalSecondaryIndex.builder()
                                    .indexName("username-index")
                                    .keySchema(
                                            KeySchemaElement.builder()
                                                    .attributeName("username")
                                                    .keyType(KeyType.HASH)
                                                    .build()
                                    )
                                    .projection(Projection.builder()
                                            .projectionType(ProjectionType.ALL)
                                            .build())
                                    .build(),
                            // 邮箱索引
                            GlobalSecondaryIndex.builder()
                                    .indexName("email-index")
                                    .keySchema(
                                            KeySchemaElement.builder()
                                                    .attributeName("email")
                                                    .keyType(KeyType.HASH)
                                                    .build()
                                    )
                                    .projection(Projection.builder()
                                            .projectionType(ProjectionType.ALL)
                                            .build())
                                    .build()
                    )
                    .billingMode(BillingMode.PAY_PER_REQUEST)
                    .build();

            CreateTableResponse response = dynamoDbClient.createTable(request);
            log.info("带GSI的用户表创建成功: {}, 状态: {}", 
                    tableName, response.tableDescription().tableStatus());
            
            // 等待表和GSI创建完成
            waitForTableAndGSIActive(tableName);
            
        } catch (ResourceInUseException e) {
            log.info("表已存在: {}", tableName);
        } catch (Exception e) {
            log.error("创建带GSI的用户表失败: {}", tableName, e);
            throw new RuntimeException("创建带GSI的用户表失败", e);
        }
    }

    /**
     * 检查表是否存在
     */
    private boolean tableExists(String tableName) {
        try {
            DescribeTableRequest request = DescribeTableRequest.builder()
                    .tableName(tableName)
                    .build();
            
            DescribeTableResponse response = dynamoDbClient.describeTable(request);
            return response.table().tableStatus() == TableStatus.ACTIVE;
        } catch (ResourceNotFoundException e) {
            return false;
        } catch (Exception e) {
            log.warn("检查表是否存在时发生错误: {}", tableName, e);
            return false;
        }
    }

    /**
     * 等待表和GSI变为活跃状态
     */
    private void waitForTableAndGSIActive(String tableName) {
        log.info("等待表和GSI变为活跃状态: {}", tableName);
        
        int maxAttempts = 60; // 最多等待2分钟
        int attempt = 0;
        
        while (attempt < maxAttempts) {
            try {
                DescribeTableRequest request = DescribeTableRequest.builder()
                        .tableName(tableName)
                        .build();
                
                DescribeTableResponse response = dynamoDbClient.describeTable(request);
                TableDescription table = response.table();
                
                boolean tableActive = table.tableStatus() == TableStatus.ACTIVE;
                boolean allGSIActive = table.globalSecondaryIndexes().stream()
                        .allMatch(gsi -> gsi.indexStatus() == IndexStatus.ACTIVE);
                
                if (tableActive && allGSIActive) {
                    log.info("表和所有GSI已变为活跃状态: {}", tableName);
                    return;
                }
                
                log.debug("等待中... 表状态: {}, GSI状态: {}", 
                        table.tableStatus(), 
                        table.globalSecondaryIndexes().stream()
                                .map(gsi -> gsi.indexName() + ":" + gsi.indexStatus())
                                .toArray());
                
                Thread.sleep(2000); // 等待2秒
                attempt++;
            } catch (Exception e) {
                log.warn("检查表和GSI状态时发生错误: {}", tableName, e);
                attempt++;
            }
        }
        
        log.warn("等待表和GSI变为活跃状态超时: {}", tableName);
    }
}
