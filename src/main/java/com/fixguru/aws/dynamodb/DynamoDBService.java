package com.fixguru.aws.dynamodb;

import com.fixguru.enums.BizCodeEnum;
import com.fixguru.exception.SystemException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import com.fixguru.domain.BaseDO;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * DynamoDB通用服务类
 * 提供基础的CRUD操作
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DynamoDBService {

    private final DynamoDbEnhancedClient enhancedClient;
    private final String tablePrefix;

    /**
     * 获取DynamoDB表
     * 
     * @param entityClass 实体类
     * @param tableName 表名
     * @return DynamoDB表对象
     */
    public <T> DynamoDbTable<T> getTable(Class<T> entityClass, String tableName) {
        String fullTableName = tablePrefix + tableName;
        return enhancedClient.table(fullTableName, TableSchema.fromBean(entityClass));
    }

    /**
     * 保存实体
     * 
     * @param table DynamoDB表
     * @param entity 实体对象
     */
    public <T> void save(DynamoDbTable<T> table, T entity) {
        try {
            table.putItem(entity);
            log.debug("保存实体成功: {}", entity);
        } catch (DynamoDbException e) {
            log.error("保存实体失败: {}", e.getMessage(), e);
            throw new SystemException(BizCodeEnum.UPDATE_ERROR.getCode(), "保存数据失败", e);
        }
    }

    /**
     * 根据主键查询实体
     * 
     * @param table DynamoDB表
     * @param partitionKey 分区键
     * @return 实体对象
     */
    public <T> Optional<T> findByKey(DynamoDbTable<T> table, String partitionKey) {
        try {
            Key key = Key.builder()
                    .partitionValue(partitionKey)
                    .build();
            T item = table.getItem(key);
            log.debug("查询实体: partitionKey={}, result={}", partitionKey, item != null ? "找到" : "未找到");
            return Optional.ofNullable(item);
        } catch (DynamoDbException e) {
            log.error("查询实体失败: partitionKey={}, error={}", partitionKey, e.getMessage(), e);
            throw new SystemException(BizCodeEnum.QUERY_ERROR.getCode(), "查询数据失败", e);
        }
    }

    /**
     * 根据主键和排序键查询实体
     * 
     * @param table DynamoDB表
     * @param partitionKey 分区键
     * @param sortKey 排序键
     * @return 实体对象
     */
    public <T> Optional<T> findByKey(DynamoDbTable<T> table, String partitionKey, String sortKey) {
        try {
            Key key = Key.builder()
                    .partitionValue(partitionKey)
                    .sortValue(sortKey)
                    .build();
            T item = table.getItem(key);
            log.debug("查询实体: partitionKey={}, sortKey={}, result={}", 
                    partitionKey, sortKey, item != null ? "找到" : "未找到");
            return Optional.ofNullable(item);
        } catch (DynamoDbException e) {
            log.error("查询实体失败: partitionKey={}, sortKey={}, error={}", 
                    partitionKey, sortKey, e.getMessage(), e);
            throw new SystemException(BizCodeEnum.QUERY_ERROR.getCode(), "查询数据失败", e);
        }
    }

    /**
     * 查询分区键下的所有实体
     * 
     * @param table DynamoDB表
     * @param partitionKey 分区键
     * @return 实体列表
     */
    public <T> List<T> queryByPartitionKey(DynamoDbTable<T> table, String partitionKey) {
        try {
            QueryConditional queryConditional = QueryConditional.keyEqualTo(
                    Key.builder().partitionValue(partitionKey).build()
            );
            
            QueryEnhancedRequest request = QueryEnhancedRequest.builder()
                    .queryConditional(queryConditional)
                    .build();
            
            List<T> items = table.query(request)
                    .items()
                    .stream()
                    .collect(Collectors.toList());
            
            log.debug("查询分区键下的实体: partitionKey={}, count={}", partitionKey, items.size());
            return items;
        } catch (DynamoDbException e) {
            log.error("查询分区键下的实体失败: partitionKey={}, error={}", partitionKey, e.getMessage(), e);
            throw new SystemException(BizCodeEnum.QUERY_ERROR.getCode(), "查询数据失败", e);
        }
    }

    /**
     * 删除实体
     * 
     * @param table DynamoDB表
     * @param partitionKey 分区键
     */
    public <T> void delete(DynamoDbTable<T> table, String partitionKey) {
        try {
            Key key = Key.builder()
                    .partitionValue(partitionKey)
                    .build();
            table.deleteItem(key);
            log.debug("删除实体成功: partitionKey={}", partitionKey);
        } catch (DynamoDbException e) {
            log.error("删除实体失败: partitionKey={}, error={}", partitionKey, e.getMessage(), e);
            throw new SystemException(BizCodeEnum.DELETE_ERROR.getCode(), "删除数据失败", e);
        }
    }

    /**
     * 删除实体（带排序键）
     * 
     * @param table DynamoDB表
     * @param partitionKey 分区键
     * @param sortKey 排序键
     */
    public <T> void delete(DynamoDbTable<T> table, String partitionKey, String sortKey) {
        try {
            Key key = Key.builder()
                    .partitionValue(partitionKey)
                    .sortValue(sortKey)
                    .build();
            table.deleteItem(key);
            log.debug("删除实体成功: partitionKey={}, sortKey={}", partitionKey, sortKey);
        } catch (DynamoDbException e) {
            log.error("删除实体失败: partitionKey={}, sortKey={}, error={}", 
                    partitionKey, sortKey, e.getMessage(), e);
            throw new SystemException(BizCodeEnum.DELETE_ERROR.getCode(), "删除数据失败", e);
        }
    }
}
