package com.fixguru.aws.lambda;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestStreamHandler;
import com.amazonaws.serverless.exceptions.ContainerInitializationException;
import com.amazonaws.serverless.proxy.spring.SpringBootLambdaContainerHandler;
import com.fixguru.Application;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * AWS Lambda Handler using AWS Serverless Java Container
 * 优化冷启动性能，完美支持Spring Boot和Knife4j的所有功能
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class SpringBootLambdaHandler implements RequestStreamHandler {

    private static SpringBootLambdaContainerHandler handler;
    private static boolean isInitialized = false;

    static {
        long startTime = System.currentTimeMillis();
        try {
            log.info("开始初始化Spring Boot Lambda容器...");

            // 设置Lambda环境变量 - 从环境变量获取，默认为dev
            String profile = System.getenv("SPRING_PROFILES_ACTIVE");
            if (profile == null || profile.isEmpty()) {
                profile = "dev";
            }
            System.setProperty("spring.profiles.active", profile);
            System.setProperty("spring.main.lazy-initialization", "true");
            System.setProperty("spring.main.banner-mode", "off");
            System.setProperty("spring.jmx.enabled", "false");

            // 使用AWS Serverless Java Container初始化Spring Boot应用
            handler = SpringBootLambdaContainerHandler.getAwsProxyHandler(Application.class);

            // 启用请求和响应压缩
            handler.stripBasePath("/api");

            isInitialized = true;
            long initTime = System.currentTimeMillis() - startTime;
            log.info("Spring Boot Lambda容器初始化完成，耗时: {}ms", initTime);

        } catch (ContainerInitializationException e) {
            log.error("Spring Boot应用初始化失败", e);
            throw new RuntimeException("Could not initialize Spring Boot application", e);
        }
    }

    @Override
    public void handleRequest(InputStream inputStream, OutputStream outputStream, Context context)
            throws IOException {

        if (!isInitialized) {
            throw new RuntimeException("Spring Boot application not initialized");
        }

        long requestStart = System.currentTimeMillis();
        try {
            // 设置Lambda上下文信息
            context.getLogger().log("Processing request: " + context.getAwsRequestId());

            // 使用AWS Serverless Java Container处理请求流
            handler.proxyStream(inputStream, outputStream, context);

            long requestTime = System.currentTimeMillis() - requestStart;
            log.debug("请求处理完成，耗时: {}ms", requestTime);

        } catch (Exception e) {
            log.error("请求处理失败: {}", e.getMessage(), e);
            throw e;
        }
    }
}
