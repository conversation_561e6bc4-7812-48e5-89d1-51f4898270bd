package com.fixguru.utils;

import java.time.Instant;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

/**
 * ID生成工具类
 * 提供各种ID生成策略
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public final class IdUtils {

    private IdUtils() {
        // 工具类，禁止实例化
    }

    /**
     * 生成UUID（去掉横线）
     *
     * @return 32位UUID字符串
     */
    public static String generateUuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成用户ID
     * 格式：user_ + 时间戳 + 随机数
     *
     * @return 用户ID
     */
    public static String generateUserId() {
        long timestamp = Instant.now().toEpochMilli();
        int random = ThreadLocalRandom.current().nextInt(1000, 9999);
        return "user_" + timestamp + "_" + random;
    }

    /**
     * 生成令牌ID
     * 格式：token_ + UUID
     *
     * @return 令牌ID
     */
    public static String generateTokenId() {
        return "token_" + generateUuid();
    }
}
