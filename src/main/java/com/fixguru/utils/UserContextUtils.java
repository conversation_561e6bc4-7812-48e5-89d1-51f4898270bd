package com.fixguru.utils;

import com.fixguru.dto.UserInfoDTO;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 用户上下文工具类
 * 用于在Controller和Service中获取当前登录用户信息
 * 支持ThreadLocal存储，实现用户资源自动隔离
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public final class UserContextUtils {

    private static final String USER_INFO_ATTRIBUTE = "userInfo";
    private static final String USER_ID_ATTRIBUTE = "userId";

    // ThreadLocal存储用户上下文，用于Service层访问
    private static final ThreadLocal<UserInfoDTO> USER_CONTEXT = new ThreadLocal<>();

    private UserContextUtils() {
        // 工具类，禁止实例化
    }

    /**
     * 设置当前用户上下文（由拦截器调用）
     *
     * @param userInfo 用户信息
     */
    public static void setCurrentUser(UserInfoDTO userInfo) {
        USER_CONTEXT.set(userInfo);
    }

    /**
     * 获取当前登录用户信息
     * 优先从ThreadLocal获取，如果没有则从Request属性获取
     *
     * @return 用户信息，如果未登录则返回null
     */
    public static UserInfoDTO getCurrentUser() {
        // 优先从ThreadLocal获取
        UserInfoDTO userInfo = USER_CONTEXT.get();
        if (userInfo != null) {
            return userInfo;
        }

        // 如果ThreadLocal中没有，从Request属性获取（兼容现有逻辑）
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return null;
        }
        return (UserInfoDTO) request.getAttribute(USER_INFO_ATTRIBUTE);
    }

    /**
     * 获取当前登录用户ID
     * 优先从ThreadLocal获取，如果没有则从Request属性获取
     *
     * @return 用户ID，如果未登录则返回null
     */
    public static String getCurrentUserId() {
        // 优先从ThreadLocal获取
        UserInfoDTO userInfo = USER_CONTEXT.get();
        if (userInfo != null) {
            return userInfo.getUserId();
        }

        // 如果ThreadLocal中没有，从Request属性获取（兼容现有逻辑）
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return null;
        }
        return (String) request.getAttribute(USER_ID_ATTRIBUTE);
    }

    /**
     * 检查是否已登录
     *
     * @return 是否已登录
     */
    public static boolean isLoggedIn() {
        return getCurrentUserId() != null;
    }

    /**
     * 清理当前用户上下文（由拦截器调用）
     * 防止内存泄漏
     */
    public static void clearCurrentUser() {
        USER_CONTEXT.remove();
    }

    /**
     * 检查当前用户是否有权限访问指定用户的资源
     *
     * @param targetUserId 目标用户ID
     * @return 是否有权限访问
     */
    public static boolean hasAccessToUser(String targetUserId) {
        String currentUserId = getCurrentUserId();
        return currentUserId != null && currentUserId.equals(targetUserId);
    }

    /**
     * 获取当前HTTP请求
     *
     * @return HttpServletRequest，如果不在请求上下文中则返回null
     */
    private static HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getRequest() : null;
    }
}
