package com.fixguru.utils;

import org.slf4j.MDC;

import java.util.UUID;

/**
 * 请求追踪工具类
 * 用于生成和管理请求追踪ID
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public final class TraceUtils {

    private TraceUtils() {
        // 工具类，禁止实例化
    }

    /**
     * 追踪ID的MDC键名
     */
    public static final String TRACE_ID_KEY = "traceId";

    /**
     * 请求ID的MDC键名
     */
    public static final String REQUEST_ID_KEY = "requestId";

    /**
     * 用户ID的MDC键名
     */
    public static final String USER_ID_KEY = "userId";

    /**
     * 生成追踪ID
     */
    public static String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成请求ID
     */
    public static String generateRequestId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 设置追踪ID到MDC
     */
    public static void setTraceId(String traceId) {
        if (traceId != null) {
            MDC.put(TRACE_ID_KEY, traceId);
        }
    }

    /**
     * 设置请求ID到MDC
     */
    public static void setRequestId(String requestId) {
        if (requestId != null) {
            MDC.put(REQUEST_ID_KEY, requestId);
        }
    }

    /**
     * 设置用户ID到MDC
     */
    public static void setUserId(String userId) {
        if (userId != null) {
            MDC.put(USER_ID_KEY, userId);
        }
    }

    /**
     * 获取当前追踪ID
     */
    public static String getTraceId() {
        return MDC.get(TRACE_ID_KEY);
    }

    /**
     * 获取当前请求ID
     */
    public static String getRequestId() {
        return MDC.get(REQUEST_ID_KEY);
    }

    /**
     * 获取当前用户ID
     */
    public static String getUserId() {
        return MDC.get(USER_ID_KEY);
    }

    /**
     * 初始化追踪上下文
     */
    public static void initTrace() {
        String traceId = generateTraceId();
        String requestId = generateRequestId();
        setTraceId(traceId);
        setRequestId(requestId);
    }

    /**
     * 初始化追踪上下文（指定追踪ID）
     */
    public static void initTrace(String traceId) {
        String requestId = generateRequestId();
        setTraceId(traceId);
        setRequestId(requestId);
    }

    /**
     * 清理追踪上下文
     */
    public static void clearTrace() {
        MDC.remove(TRACE_ID_KEY);
        MDC.remove(REQUEST_ID_KEY);
        MDC.remove(USER_ID_KEY);
    }

    /**
     * 清理所有MDC
     */
    public static void clearAll() {
        MDC.clear();
    }

    /**
     * 获取追踪信息摘要
     */
    public static String getTraceSummary() {
        String traceId = getTraceId();
        String requestId = getRequestId();
        String userId = getUserId();
        
        StringBuilder summary = new StringBuilder();
        if (traceId != null) {
            summary.append("traceId=").append(traceId);
        }
        if (requestId != null) {
            if (summary.length() > 0) summary.append(", ");
            summary.append("requestId=").append(requestId);
        }
        if (userId != null) {
            if (summary.length() > 0) summary.append(", ");
            summary.append("userId=").append(userId);
        }
        
        return summary.toString();
    }
}
