package com.fixguru.controller;


import com.fixguru.common.ApiResponse;
import com.fixguru.constants.SystemConstants;
import com.fixguru.dto.AuthTokenDTO;
import com.fixguru.dto.UserInfoDTO;
import com.fixguru.dto.request.ForgotPasswordRequest;
import com.fixguru.dto.request.ResetPasswordRequest;
import com.fixguru.dto.request.UserCreateRequest;
import com.fixguru.dto.request.UserLoginRequest;
import com.fixguru.enums.BizCodeEnum;
import com.fixguru.exception.BizException;
import com.fixguru.service.TokenService;
import com.fixguru.service.UserService;
import com.fixguru.utils.JwtUtils;
import com.fixguru.utils.UserContextUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 用户控制器
 * 提供用户相关的API接口
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/v1/users")
@RequiredArgsConstructor
@Validated
@Tag(name = "用户管理", description = "用户相关的所有接口，按功能分组：用户CRUD、认证操作、状态检查")
public class UserController {

    private final UserService userService;
    private final TokenService tokenService;


    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "创建新用户账户，无需认证，返回用户基本信息")
    public ApiResponse<UserInfoDTO> register(@Valid @RequestBody UserCreateRequest request) {
        log.info("用户注册请求: firstName={}, email={}", request.getFirstName(), request.getEmail());
        UserInfoDTO userInfoDTO = userService.createUser(request);
        return ApiResponse.success(BizCodeEnum.REGISTRATION_SUCCESS.getMessage(), userInfoDTO);
    }

    /**
     * 根据用户ID获取用户信息
     * 注意：由于权限控制，用户只能查看自己的信息
     */
    @GetMapping("/{userId}")
    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户详细信息，权限控制用户只能查看自己的信息",
            security = @SecurityRequirement(name = "FixGuru-Auth"))
    public ApiResponse<UserInfoDTO> getUserById(
            @Parameter(description = "用户ID（必须是当前登录用户的ID）", required = true)
            @PathVariable @NotBlank(message = "User ID is required") String userId) {

        UserInfoDTO userInfoDTO = userService.getUserById(userId);
        return ApiResponse.success(userInfoDTO);
    }

    /**
     * 根据用户名获取用户信息
     */
    @GetMapping("/username/{username}")
    @Operation(summary = "根据用户名获取用户信息", description = "需要认证，允许查询任何用户信息，仅用于系统内部功能",
            security = @SecurityRequirement(name = "FixGuru-Auth"))
    public ApiResponse<UserInfoDTO> getUserByUsername(
            @Parameter(description = "用户名", required = true)
            @PathVariable @NotBlank(message = "Username is required") String username) {

        UserInfoDTO userInfoDTO = userService.getUserByUsername(username);
        return ApiResponse.success(userInfoDTO);
    }

    /**
     * 根据邮箱获取用户信息
     */
    @GetMapping("/email/{email}")
    @Operation(summary = "根据邮箱获取用户信息", description = "需要认证，允许查询任何用户信息，仅用于系统内部功能",
            security = @SecurityRequirement(name = "FixGuru-Auth"))
    public ApiResponse<UserInfoDTO> getUserByEmail(
            @Parameter(description = "邮箱", required = true)
            @PathVariable @NotBlank(message = "Email is required") String email) {

        UserInfoDTO userInfoDTO = userService.getUserByEmail(email);
        return ApiResponse.success(userInfoDTO);
    }


    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "获取访问令牌和刷新令牌，无需认证，返回token用于后续API调用")
    public ApiResponse<AuthTokenDTO> login(@Valid @RequestBody UserLoginRequest request) {
        log.info("用户登录请求: email={}", request.getEmail());
        AuthTokenDTO authTokenDTO = userService.login(request);
        return ApiResponse.success(BizCodeEnum.LOGIN_SUCCESS.getMessage(), authTokenDTO);
    }

    /**
     * 忘记密码
     */
    @PostMapping("/forgot-password")
    @Operation(summary = "忘记密码", description = "发送重置密码邮件，无需认证，用户可通过邮件重置密码")
    public ApiResponse<Void> forgotPassword(@Valid @RequestBody ForgotPasswordRequest request) {
        log.info("忘记密码请求: email={}", request.getEmail());
        userService.forgotPassword(request);
        return ApiResponse.success(BizCodeEnum.PASSWORD_RESET_EMAIL_SENT.getMessage(), null);
    }

    /**
     * 重置密码
     */
    @PostMapping("/reset-password")
    @Operation(summary = "重置密码", description = "使用重置令牌设置新密码，无需认证，用户通过邮件中的链接访问")
    public ApiResponse<Void> resetPassword(@Valid @RequestBody ResetPasswordRequest request) {
        log.info("重置密码请求");
        userService.resetPassword(request);
        return ApiResponse.success("Password reset successfully", null);
    }

    /**
     * 刷新访问令牌
     */
    @PostMapping("/refresh-token")
    @Operation(summary = "刷新访问令牌",
               description = "使用刷新令牌获取新的访问令牌，无需认证。请在Authorization header中传递refresh token，格式：Authorization: refresh_xxx",
               security = @SecurityRequirement(name = "FixGuru-Auth"))
    public ApiResponse<AuthTokenDTO> refreshToken(HttpServletRequest request) {
        log.info("刷新令牌请求");

        // 从请求头获取refresh token
        String refreshToken = JwtUtils.extractRefreshTokenFromRequest(request);

        AuthTokenDTO authTokenDTO = tokenService.refreshAccessToken(refreshToken);
        return ApiResponse.success(BizCodeEnum.TOKEN_REFRESHED.getMessage(), authTokenDTO);
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "让所有token失效并记录登出操作，客户端应清除本地存储的所有token",
            security = @SecurityRequirement(name = "FixGuru-Auth"))
    public ApiResponse<Map<String, Object>> logout(HttpServletRequest request) {

        String currentUserId = UserContextUtils.getCurrentUserId();

        // 从请求头中获取access token
        String accessToken = JwtUtils.extractAccessTokenFromRequest(request);

        log.info("用户登出请求: userId={}", currentUserId);

        // 执行登出逻辑 - 让所有token失效
        Map<String, Object> logoutResult = tokenService.logout(accessToken, currentUserId);

        return ApiResponse.success(BizCodeEnum.LOGOUT_SUCCESS.getMessage(), logoutResult);
    }


    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check/username/{username}")
    @Operation(summary = "检查用户名是否存在", description = "检查指定用户名是否已被使用，无需认证，用于注册时验证用户名可用性")
    public ApiResponse<Boolean> checkUsernameExists(
            @Parameter(description = "用户名", required = true)
            @PathVariable @NotBlank(message = "Username is required") String username) {
        boolean exists = userService.existsByUsername(username);
        return ApiResponse.success(exists);
    }

    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/check/email/{email}")
    @Operation(summary = "检查邮箱是否存在", description = "检查指定邮箱是否已被使用，无需认证，用于注册时验证邮箱可用性")
    public ApiResponse<Boolean> checkEmailExists(
            @Parameter(description = "邮箱", required = true)
            @PathVariable @NotBlank(message = "Email is required") String email) {
        boolean exists = userService.existsByEmail(email);
        return ApiResponse.success(exists);
    }

    /**
     * 检查用户状态
     */
    @GetMapping("/{userId}/status")
    @Operation(summary = "检查用户状态", description = "检查用户当前状态和权限，需要认证，用户只能查看自己的状态信息",
            security = @SecurityRequirement(name = "FixGuru-Auth"))
    public ApiResponse<Map<String, Object>> checkUserStatus(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotBlank(message = "User ID is required") String userId) {

        Map<String, Object> statusInfo = userService.getUserStatus(userId);
        return ApiResponse.success(statusInfo);
    }


}
