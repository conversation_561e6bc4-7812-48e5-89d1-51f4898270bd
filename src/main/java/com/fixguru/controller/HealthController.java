package com.fixguru.controller;

import com.fixguru.common.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.model.ListTablesRequest;
import software.amazon.awssdk.services.dynamodb.model.ListTablesResponse;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 提供系统健康状态检查接口
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/v1/health")
@Tag(name = "健康检查", description = "系统健康状态检查接口")
public class HealthController {

    private final DynamoDbClient dynamoDbClient;

    public HealthController(DynamoDbClient dynamoDbClient) {
        this.dynamoDbClient = dynamoDbClient;
    }

    @Value("${app.name:FixGuru}")
    private String appName;

    @Value("${app.version:1.0.0}")
    private String appVersion;

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Value("${aws.region:us-west-1}")
    private String awsRegion;

    @Value("${app.health-check.dynamodb.enabled:true}")
    private boolean dynamoDbHealthCheckEnabled;


    /**
     * 健康检查
     */
    @GetMapping
    @Operation(summary = "基础健康检查", description = "检查系统基本运行状态，无需认证，返回系统状态、版本信息等")
    public ApiResponse<Map<String, Object>> health() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("timestamp", Instant.now());
        healthInfo.put("application", appName);
        healthInfo.put("version", appVersion);
        healthInfo.put("profile", activeProfile);
        healthInfo.put("uptime", getUptime());

        log.debug("健康检查请求");
        return ApiResponse.success("System is running normally", healthInfo);
    }


    /**
     * 详细健康检查
     */
    @GetMapping("/detailed")
    @Operation(summary = "详细健康检查", description = "详细的系统状态检查，包括AWS DynamoDB连接状态、内存使用情况等，无需认证")
    public ApiResponse<Map<String, Object>> detailedHealth() {
        Map<String, Object> detailedInfo = new HashMap<>();
        boolean overallStatus = true;

        // 基本信息
        detailedInfo.put("timestamp", Instant.now());
        detailedInfo.put("application", appName);
        detailedInfo.put("version", appVersion);
        detailedInfo.put("profile", activeProfile);
        detailedInfo.put("region", awsRegion);

        // 系统信息
        Runtime runtime = Runtime.getRuntime();
        Map<String, Object> systemInfo = new HashMap<>();
        systemInfo.put("processors", runtime.availableProcessors());
        systemInfo.put("totalMemory", runtime.totalMemory());
        systemInfo.put("freeMemory", runtime.freeMemory());
        systemInfo.put("maxMemory", runtime.maxMemory());
        systemInfo.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
        detailedInfo.put("system", systemInfo);

        // JVM信息
        Map<String, Object> jvmInfo = new HashMap<>();
        jvmInfo.put("version", System.getProperty("java.version"));
        jvmInfo.put("vendor", System.getProperty("java.vendor"));
        jvmInfo.put("home", System.getProperty("java.home"));
        detailedInfo.put("jvm", jvmInfo);

        // AWS服务健康检查
        Map<String, Object> awsServices = new HashMap<>();

        // DynamoDB健康检查
        if (dynamoDbHealthCheckEnabled) {
            Map<String, Object> dynamoDbStatus = checkDynamoDbHealth();
            awsServices.put("dynamodb", dynamoDbStatus);
            if (!"UP".equals(dynamoDbStatus.get("status"))) {
                overallStatus = false;
            }
        }

        detailedInfo.put("awsServices", awsServices);

        // 运行时间
        detailedInfo.put("uptime", getUptime());

        // 设置总体状态
        detailedInfo.put("status", overallStatus ? "UP" : "DOWN");

        log.debug("详细健康检查请求，总体状态: {}", overallStatus ? "UP" : "DOWN");
        return ApiResponse.success(overallStatus ? "System is running normally" : "System has issues", detailedInfo);
    }


    /**
     * 获取系统运行时间
     */
    private String getUptime() {
        long uptimeMillis = System.currentTimeMillis() - getStartTime();
        long seconds = uptimeMillis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;

        return String.format("%d days %d hours %d minutes %d seconds",
                days, hours % 24, minutes % 60, seconds % 60);
    }

    /**
     * 获取系统启动时间（简化处理）
     */
    private long getStartTime() {
        // 这里简化处理，实际项目中可以在应用启动时记录启动时间
        return System.currentTimeMillis() -
                java.lang.management.ManagementFactory.getRuntimeMXBean().getUptime();
    }

    /**
     * 检查DynamoDB健康状态
     */
    private Map<String, Object> checkDynamoDbHealth() {
        Map<String, Object> status = new HashMap<>();
        long startTime = System.currentTimeMillis();

        try {
            // 尝试列出表来验证连接
            ListTablesRequest request = ListTablesRequest.builder()
                    .limit(1)
                    .build();

            ListTablesResponse response = dynamoDbClient.listTables(request);
            long responseTime = System.currentTimeMillis() - startTime;

            status.put("status", "UP");
            status.put("responseTime", responseTime + "ms");
            status.put("tablesCount", response.tableNames().size());
            status.put("region", awsRegion);
            status.put("message", "DynamoDB connection is healthy");

            log.debug("DynamoDB健康检查成功，响应时间: {}ms", responseTime);

        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            status.put("status", "DOWN");
            status.put("responseTime", responseTime + "ms");
            status.put("error", e.getMessage());
            status.put("region", awsRegion);
            status.put("message", "DynamoDB connection failed");

            log.error("DynamoDB健康检查失败: {}", e.getMessage());
        }

        return status;
    }


}
