package com.fixguru.domain;

import lombok.Data;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute;

import java.time.Instant;

/**
 * DynamoDB实体基类
 * 包含通用的审计字段
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public abstract class BaseDO {

    /**
     * 创建时间
     */
    private Instant createdTime;

    /**
     * 更新时间
     */
    private Instant updatedTime;

    /**
     * 创建者
     */
    private String createdBy;

    /**
     * 更新者
     */
    private String updatedBy;

    /**
     * 版本号（用于乐观锁）
     */
    private Long version;

    /**
     * 是否删除（软删除标记）
     */
    private Boolean deleted = false;

    // DynamoDB属性映射方法（按字段声明顺序排列）
    @DynamoDbAttribute("createdTime")
    public Instant getCreatedTime() {
        return createdTime;
    }

    @DynamoDbAttribute("updatedTime")
    public Instant getUpdatedTime() {
        return updatedTime;
    }

    @DynamoDbAttribute("createdBy")
    public String getCreatedBy() {
        return createdBy;
    }

    @DynamoDbAttribute("updatedBy")
    public String getUpdatedBy() {
        return updatedBy;
    }

    @DynamoDbAttribute("version")
    public Long getVersion() {
        return version;
    }

    @DynamoDbAttribute("deleted")
    public Boolean getDeleted() {
        return deleted;
    }
}
