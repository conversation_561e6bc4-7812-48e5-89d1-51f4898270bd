package com.fixguru.config;

import io.jsonwebtoken.security.Keys;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * JWT配置类
 * 从配置文件中读取JWT相关配置
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app.security")
public class JwtConfig {

    /**
     * JWT密钥（将通过SHA-256哈希确保足够的长度）
     */
    private String jwtSecret = "FixGuru-Default-JWT-Secret-Key-2024-Please-Change-In-Production-Environment-For-Security";

    /**
     * Access Token过期时间（小时）
     */
    private Integer accessTokenExpirationHours = 24;

    /**
     * Refresh Token过期时间（天）
     */
    private Integer refreshTokenExpirationDays = 30;

    /**
     * 最大登录尝试次数
     */
    private Integer maxLoginAttempts = 5;

    /**
     * 账户锁定时间（分钟）
     */
    private Integer accountLockMinutes = 30;

    /**
     * 是否启用CORS
     */
    private Boolean corsEnabled = true;

    /**
     * 获取JWT密钥字节数组（使用SHA-256确保256位长度）
     */
    public byte[] getJwtSecretBytes() {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            return digest.digest(jwtSecret.getBytes(StandardCharsets.UTF_8));
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }

    /**
     * 获取JWT签名密钥（推荐使用此方法）
     */
    public SecretKey getSigningKey() {
        return Keys.hmacShaKeyFor(getJwtSecretBytes());
    }

    /**
     * 获取Access Token过期时间（秒）
     */
    public long getAccessTokenExpirationSeconds() {
        return accessTokenExpirationHours * 3600L;
    }

    /**
     * 获取Refresh Token过期时间（秒）
     */
    public long getRefreshTokenExpirationSeconds() {
        return refreshTokenExpirationDays * 24 * 3600L;
    }
}
