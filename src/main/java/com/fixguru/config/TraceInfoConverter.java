package com.fixguru.config;

import ch.qos.logback.classic.pattern.ClassicConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import com.fixguru.utils.TraceUtils;
import org.slf4j.MDC;

/**
 * 链路追踪信息转换器
 * 只有存在值时才显示对应的追踪标签，避免空标签的显示
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class TraceInfoConverter extends ClassicConverter {

    @Override
    public String convert(ILoggingEvent event) {
        StringBuilder sb = new StringBuilder();
        
        String traceId = MDC.get(TraceUtils.TRACE_ID_KEY);
        String requestId = MDC.get(TraceUtils.REQUEST_ID_KEY);
        String userId = MDC.get(TraceUtils.USER_ID_KEY);
        
        if (traceId != null && !traceId.isEmpty()) {
            sb.append("[traceId:").append(traceId).append("] ");
        }
        
        if (requestId != null && !requestId.isEmpty()) {
            sb.append("[requestId:").append(requestId).append("] ");
        }
        
        if (userId != null && !userId.isEmpty()) {
            sb.append("[userId:").append(userId).append("] ");
        }
        
        return sb.toString();
    }
}
