package com.fixguru.config;

import com.fixguru.constants.EmailConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 * 统一管理各业务场景的线程池配置
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Configuration
@EnableAsync
public class ThreadPoolConfig {

    /**
     * 邮件发送异步执行器
     */
    @Bean("emailTaskExecutor")
    public Executor emailTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 使用常量配置线程池参数
        executor.setCorePoolSize(EmailConstants.EMAIL_THREAD_CORE_POOL_SIZE);
        executor.setMaxPoolSize(EmailConstants.EMAIL_THREAD_MAX_POOL_SIZE);
        executor.setQueueCapacity(EmailConstants.EMAIL_THREAD_QUEUE_CAPACITY);
        executor.setThreadNamePrefix("email-async-");
        executor.setKeepAliveSeconds(EmailConstants.EMAIL_THREAD_KEEP_ALIVE_SECONDS);
        
        // 拒绝策略：调用者运行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(EmailConstants.EMAIL_THREAD_SHUTDOWN_TIMEOUT_SECONDS);
        
        executor.initialize();
        
        log.info("邮件异步执行器初始化完成: corePoolSize={}, maxPoolSize={}, queueCapacity={}", 
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
        
        return executor;
    }
}
