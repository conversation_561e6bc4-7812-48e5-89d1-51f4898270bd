package com.fixguru.config;

import com.fixguru.constants.DynamoDBConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.core.client.config.ClientOverrideConfiguration;
import software.amazon.awssdk.core.retry.RetryPolicy;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.http.SdkHttpClient;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.dynamodb.DynamoDbClientBuilder;

import java.time.Duration;

/**
 * DynamoDB配置类
 * 根据不同环境配置DynamoDB客户端
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-27
 */
@Slf4j
@Configuration
public class DynamoDBConfig {

    @Value("${aws.region:us-west-1}")
    private String awsRegion;

    @Value("${aws.dynamodb.table-prefix:}")
    private String tablePrefix;

    /**
     * DynamoDB客户端配置
     */
    @Bean
    @Primary
    public DynamoDbClient dynamoDbClient() {
        try {
            // Apache HTTP客户端配置 - 连接池优化
            SdkHttpClient httpClient = ApacheHttpClient.builder()
                    // 连接池配置
                    .maxConnections(DynamoDBConstants.MAX_CONNECTIONS)
                    .connectionMaxIdleTime(Duration.ofMinutes(DynamoDBConstants.CONNECTION_MAX_IDLE_MINUTES))
                    .connectionTimeToLive(Duration.ofMinutes(DynamoDBConstants.CONNECTION_TIME_TO_LIVE_MINUTES))
                    // 超时配置
                    .connectionTimeout(Duration.ofMillis(DynamoDBConstants.CONNECTION_TIMEOUT_MS))
                    .socketTimeout(Duration.ofMillis(DynamoDBConstants.SOCKET_TIMEOUT_MS))
                    // 连接池管理，启用空闲连接回收
                    .useIdleConnectionReaper(true)
                    .build();

            // 客户端超时和重试配置 - 使用常量配置，优化网络性能
            ClientOverrideConfiguration clientConfig = ClientOverrideConfiguration.builder()
                    .apiCallTimeout(Duration.ofSeconds(DynamoDBConstants.API_CALL_TIMEOUT_SECONDS))
                    .apiCallAttemptTimeout(Duration.ofSeconds(DynamoDBConstants.API_CALL_ATTEMPT_TIMEOUT_SECONDS))
                    .retryPolicy(RetryPolicy.builder().numRetries(DynamoDBConstants.RETRY_MAX_ATTEMPTS).build())
                    .build();

            DynamoDbClientBuilder builder = DynamoDbClient.builder()
                    .region(Region.of(awsRegion))
                    .httpClient(httpClient)
                    .overrideConfiguration(clientConfig);

            // 配置AWS DynamoDB环境（统一使用AWS DynamoDB）
            configureAwsDynamoDB(builder, awsRegion);

            return builder.build();
        } catch (Exception e) {
            log.warn("DynamoDB客户端配置失败，将使用模拟客户端: {}", e.getMessage());
            // 返回一个模拟的客户端，避免应用启动失败
            return createMockDynamoDbClient();
        }
    }

    /**
     * 创建模拟的DynamoDB客户端
     */
    private DynamoDbClient createMockDynamoDbClient() {
        log.warn("DynamoDB客户端配置失败，创建模拟客户端");
        return DynamoDbClient.builder()
                .region(Region.of(awsRegion))
                .credentialsProvider(DefaultCredentialsProvider.create())
                .build();
    }

    /**
     * DynamoDB增强客户端配置
     */
    @Bean
    @Primary
    public DynamoDbEnhancedClient dynamoDbEnhancedClient(DynamoDbClient dynamoDbClient) {
        return DynamoDbEnhancedClient.builder()
                .dynamoDbClient(dynamoDbClient)
                .build();
    }

    /**
     * 表名前缀配置
     */
    @Bean
    public String tablePrefix() {
        return tablePrefix != null ? tablePrefix : "";
    }

    /**
     * 配置AWS DynamoDB环境
     */
    private void configureAwsDynamoDB(DynamoDbClientBuilder builder, String awsRegion) {
        log.info("配置DynamoDB AWS环境: region={}, 连接池: maxConnections={}, connectionTimeout={}ms, socketTimeout={}ms, idleTime={}min, ttl={}min",
                awsRegion,
                DynamoDBConstants.MAX_CONNECTIONS,
                DynamoDBConstants.CONNECTION_TIMEOUT_MS,
                DynamoDBConstants.SOCKET_TIMEOUT_MS,
                DynamoDBConstants.CONNECTION_MAX_IDLE_MINUTES,
                DynamoDBConstants.CONNECTION_TIME_TO_LIVE_MINUTES);
        builder.credentialsProvider(DefaultCredentialsProvider.create());
    }

}
