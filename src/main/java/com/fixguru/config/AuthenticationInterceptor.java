package com.fixguru.config;

import com.fixguru.constants.SystemConstants;
import com.fixguru.dto.UserInfoDTO;
import com.fixguru.enums.BizCodeEnum;
import com.fixguru.exception.BizException;
import com.fixguru.service.TokenService;
import com.fixguru.utils.JwtUtils;
import com.fixguru.utils.TraceUtils;
import com.fixguru.utils.UserContextUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 认证拦截器
 * 拦截需要认证的接口，验证JWT token
 * 将用户信息存储到ThreadLocal中，实现用户资源自动隔离
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-31
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthenticationInterceptor implements HandlerInterceptor {

    private final TokenService tokenService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // OPTIONS请求直接放行
        if ("OPTIONS".equals(request.getMethod())) {
            return true;
        }

        log.debug("认证拦截器处理请求: {} {}", request.getMethod(), request.getRequestURI());

        // 获取Authorization头
        String authHeader = request.getHeader(SystemConstants.SECURITY_AUTHORIZATION_HEADER);
        if (authHeader == null || authHeader.trim().isEmpty()) {
            log.warn("认证失败: 缺少Authorization头 - URI: {}, Method: {}", request.getRequestURI(), request.getMethod());
            throw new BizException(BizCodeEnum.UNAUTHORIZED, "Missing Authorization header");
        }

        // 直接使用Authorization头的值作为token（支持access_xxx格式）
        String accessToken = authHeader.trim();
        log.debug("获取到token: {} (前20字符)", accessToken.length() > 20 ? accessToken.substring(0, 20) + "..." : accessToken);

        // 获取用户信息（先获取用户信息以便进行登出时间检查）
        UserInfoDTO userInfo = tokenService.getUserInfoFromAccessToken(accessToken);
        if (userInfo == null) {
            log.warn("认证失败: 无法获取用户信息 - URI: {}, Method: {}", request.getRequestURI(), request.getMethod());
            throw new BizException(BizCodeEnum.UNAUTHORIZED, "Unable to extract user information from token");
        }

        // 验证token有效性（包含登出时间检查）
        if (!tokenService.validateAccessTokenWithLogoutCheck(accessToken, userInfo.getUserId())) {
            log.warn("认证失败: token无效、已过期或已登出 - URI: {}, Method: {}, userId: {}, Token前缀: {}",
                    request.getRequestURI(), request.getMethod(), userInfo.getUserId(),
                    accessToken.length() > 10 ? accessToken.substring(0, 10) + "..." : accessToken);
            throw new BizException(BizCodeEnum.UNAUTHORIZED, "Invalid, expired or revoked access token");
        }

        // 将用户信息存储到请求属性中（兼容现有逻辑）
        request.setAttribute(SystemConstants.REQUEST_ATTRIBUTE_USER_INFO, userInfo);
        request.setAttribute(SystemConstants.REQUEST_ATTRIBUTE_USER_ID, userInfo.getUserId());

        // 将用户信息存储到ThreadLocal中（新的资源隔离机制）
        UserContextUtils.setCurrentUser(userInfo);

        // 将用户ID添加到MDC中，用于日志追踪
        TraceUtils.setUserId(userInfo.getUserId());

        log.debug("认证成功: userId={}, URI={}, Method={}", userInfo.getUserId(), request.getRequestURI(), request.getMethod());
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 清理ThreadLocal，防止内存泄漏
        UserContextUtils.clearCurrentUser();
        log.debug("已清理用户上下文: URI={}", request.getRequestURI());
    }
}
