package com.fixguru.config;

import io.micrometer.core.aop.TimedAspect;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.actuate.health.Status;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * 监控配置类
 * 企业级性能监控和健康检查配置
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-07-27
 */
@Configuration
public class MonitorConfig {

    /**
     * 启用@Timed注解支持
     */
    @Bean
    public TimedAspect timedAspect(MeterRegistry registry) {
        return new TimedAspect(registry);
    }

    /**
     * 自定义健康检查指示器
     */
    @Bean
    public HealthIndicator platformHealthIndicator() {
        return () -> {
            // 检查平台核心组件状态
            boolean isHealthy = checkPlatformHealth();
            
            if (isHealthy) {
                return org.springframework.boot.actuate.health.Health.up()
                    .withDetail("platform", "All systems operational")
                    .withDetail("version", "1.0.0")
                    .withDetail("environment", getEnvironment())
                    .build();
            } else {
                return org.springframework.boot.actuate.health.Health.down()
                    .withDetail("platform", "System degraded")
                    .withDetail("reason", "Core components unavailable")
                    .build();
            }
        };
    }

    /**
     * DynamoDB健康检查指示器
     */
    @Bean
    public HealthIndicator dynamodbHealthIndicator() {
        return () -> {
            try {
                // 这里可以添加DynamoDB连接检查逻辑
                // 例如：执行一个简单的查询或列表表操作
                boolean isConnected = checkDynamoDBConnection();
                
                if (isConnected) {
                    return org.springframework.boot.actuate.health.Health.up()
                        .withDetail("service", "DynamoDB")
                        .withDetail("status", "Connected")
                        .build();
                } else {
                    return org.springframework.boot.actuate.health.Health.down()
                        .withDetail("service", "DynamoDB")
                        .withDetail("status", "Connection failed")
                        .build();
                }
            } catch (Exception e) {
                return org.springframework.boot.actuate.health.Health.down()
                    .withDetail("service", "DynamoDB")
                    .withDetail("error", e.getMessage())
                    .build();
            }
        };
    }

    /**
     * 自定义性能指标
     */
    @Bean
    public Timer customTimer(MeterRegistry meterRegistry) {
        return Timer.builder("platform.operation")
            .description("Platform operation execution time")
            .register(meterRegistry);
    }

    /**
     * 检查平台健康状态
     */
    private boolean checkPlatformHealth() {
        // 实现平台核心组件健康检查逻辑
        // 例如：检查关键服务、数据库连接、外部依赖等
        try {
            // 检查JVM内存使用情况
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            // 如果内存使用超过90%，认为不健康
            double memoryUsageRatio = (double) usedMemory / maxMemory;
            if (memoryUsageRatio > 0.9) {
                return false;
            }
            
            // 检查磁盘空间
            // 这里可以添加更多检查逻辑
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查DynamoDB连接
     */
    private boolean checkDynamoDBConnection() {
        // 实现DynamoDB连接检查逻辑
        // 这里可以注入DynamoDB客户端并执行简单操作
        try {
            // 示例：列出表或执行简单查询
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取当前环境
     */
    private String getEnvironment() {
        return System.getProperty("spring.profiles.active", "unknown");
    }
}
