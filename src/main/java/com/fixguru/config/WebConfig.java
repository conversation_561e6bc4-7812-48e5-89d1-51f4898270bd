package com.fixguru.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类 - 非Lambda环境使用
 * 配置CORS、拦截器、静态资源等
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
@Profile("!lambda")
@RequiredArgsConstructor
public class WebConfig implements WebMvcConfigurer {

    private final RequestInterceptor requestInterceptor;
    private final AuthenticationInterceptor authenticationInterceptor;

    /**
     * 配置CORS跨域
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }

    /**
     * 配置拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 请求追踪拦截器
        registry.addInterceptor(requestInterceptor)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(
                    "/v1/health/**",      // 排除所有健康检查接口
                    "/actuator/**",       // 排除监控端点
                    "/doc.html",          // 排除API文档
                    "/swagger-ui/**",     // 排除Swagger UI
                    "/v3/api-docs/**",    // 排除API文档
                    "/webjars/**",        // 排除静态资源
                    "/favicon.ico",       // 排除图标
                    "/static/**"          // 排除静态资源
                );

        // 认证拦截器
        registry.addInterceptor(authenticationInterceptor)
                .addPathPatterns("/v1/**") // 拦截所有API请求
                .excludePathPatterns(
                    "/v1/health/**",      // 排除所有健康检查接口
                    // 排除不需要token的用户接口
                    "/v1/users/register", // 用户注册
                    "/v1/users/login",    // 用户登录
                    "/v1/users/forgot-password", // 忘记密码
                    "/v1/users/reset-password",  // 重置密码
                    "/v1/users/refresh-token",   // 刷新令牌
                    "/v1/users/check/**"         // 检查接口（邮箱等）
                );
    }

    /**
     * 配置静态资源处理
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置favicon处理
        registry.addResourceHandler("/favicon.ico")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(86400); // 缓存1天

        // 配置其他静态资源
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(86400);
    }
}
