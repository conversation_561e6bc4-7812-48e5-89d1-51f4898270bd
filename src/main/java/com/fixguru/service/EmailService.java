package com.fixguru.service;

/**
 * 邮件服务接口
 * 提供邮件发送相关的业务逻辑处理
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface EmailService {

    /**
     * 发送重置密码邮件（异步）
     * 不阻塞主线程，提高响应速度
     *
     * @param toEmail 收件人邮箱
     * @param resetToken 重置令牌
     * @param userName 用户名
     */
    void sendPasswordResetEmail(String toEmail, String resetToken, String userName);

    /**
     * 检查邮件发送频率限制
     * 防止同一邮箱短时间内重复发送重置邮件
     *
     * @param email 邮箱地址
     * @return 是否可以发送（true表示可以发送，false表示需要等待）
     */
    boolean canSendResetEmail(String email);

    /**
     * 记录邮件发送时间
     * 用于频率限制控制
     *
     * @param email 邮箱地址
     */
    void recordEmailSentTime(String email);

    /**
     * 检查每日邮件发送次数限制
     * 防止同一邮箱每天发送过多重置邮件
     *
     * @param email 邮箱地址
     * @return 是否可以发送（true表示未超过每日限制，false表示已超过）
     */
    boolean canSendDailyLimit(String email);

    /**
     * 记录每日邮件发送次数
     * 用于每日限制控制
     *
     * @param email 邮箱地址
     */
    void recordDailySendCount(String email);
}
