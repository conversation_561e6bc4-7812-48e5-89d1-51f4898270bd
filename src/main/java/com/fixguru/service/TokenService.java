package com.fixguru.service;

import com.fixguru.dto.AuthTokenDTO;
import com.fixguru.dto.UserInfoDTO;

import java.util.Map;

/**
 * 令牌服务接口
 * 提供令牌生成、验证、刷新等功能
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface TokenService {

    /**
     * 生成认证令牌
     *
     * @param userInfo 用户信息
     * @return 认证令牌DTO
     */
    AuthTokenDTO generateAuthToken(UserInfoDTO userInfo);

    /**
     * 刷新访问令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的认证令牌DTO
     */
    AuthTokenDTO refreshAccessToken(String refreshToken);



    /**
     * 验证访问令牌
     *
     * @param accessToken 访问令牌
     * @return 是否有效
     */
    boolean validateAccessToken(String accessToken);

    /**
     * 验证访问令牌（包含登出时间检查）
     *
     * @param accessToken 访问令牌
     * @param userId      用户ID
     * @return 是否有效
     */
    boolean validateAccessTokenWithLogoutCheck(String accessToken, String userId);

    /**
     * 验证刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 是否有效
     */
    boolean validateRefreshToken(String refreshToken);

    /**
     * 验证刷新令牌（包含登出时间检查）
     *
     * @param refreshToken 刷新令牌
     * @param userId       用户ID
     * @return 是否有效
     */
    boolean validateRefreshTokenWithLogoutCheck(String refreshToken, String userId);

    /**
     * 从访问令牌中获取用户信息
     *
     * @param accessToken 访问令牌
     * @return 用户信息
     */
    UserInfoDTO getUserInfoFromAccessToken(String accessToken);

    /**
     * 检查访问令牌是否需要刷新
     *
     * @param accessToken 访问令牌
     * @return 是否需要刷新
     */
    boolean shouldRefreshAccessToken(String accessToken);


    /**
     * 用户登出
     * 让该用户的所有token失效，包括access token和refresh token
     *
     * @param accessToken 当前访问令牌
     * @param userId      用户ID
     * @return 登出结果信息
     */
    Map<String, Object> logout(String accessToken, String userId);
}
