package com.fixguru.service.impl;

import com.fixguru.config.JwtConfig;
import com.fixguru.constants.SystemConstants;
import com.fixguru.dto.AuthTokenDTO;
import com.fixguru.dto.UserInfoDTO;
import com.fixguru.aws.dynamodb.DynamoDBService;
import com.fixguru.domain.UserDO;
import com.fixguru.enums.BizCodeEnum;
import com.fixguru.exception.BizException;
import com.fixguru.service.TokenService;
import com.fixguru.utils.JwtUtils;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;

/**
 * 令牌服务实现类
 * 实现JWT令牌的生成、验证、刷新等功能
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TokenServiceImpl implements TokenService {

    private final JwtUtils jwtUtils;
    private final JwtConfig jwtConfig;
    private final DynamoDBService dynamoDBService;

    @Override
    public AuthTokenDTO generateAuthToken(UserInfoDTO userInfo) {
        log.info("为用户生成认证令牌: userId={}", userInfo.getUserId());

        // 生成访问令牌和刷新令牌
        String accessToken = jwtUtils.generateAccessToken(userInfo);
        String refreshToken = jwtUtils.generateRefreshToken(userInfo);

        // 创建认证令牌DTO
        AuthTokenDTO authTokenDTO = new AuthTokenDTO();
        authTokenDTO.setAccessToken(accessToken);
        authTokenDTO.setRefreshToken(refreshToken);
        authTokenDTO.setExpiresIn(jwtConfig.getAccessTokenExpirationSeconds());
        authTokenDTO.setRefreshExpiresIn(jwtConfig.getRefreshTokenExpirationSeconds());
        authTokenDTO.setIssuedAt(Instant.now());
        authTokenDTO.setUserInfo(userInfo);

        log.info("认证令牌生成成功: userId={}, expiresIn={}s, refreshExpiresIn={}s",
                userInfo.getUserId(),
                authTokenDTO.getExpiresIn(),
                authTokenDTO.getRefreshExpiresIn());

        return authTokenDTO;
    }

    @Override
    public AuthTokenDTO refreshAccessToken(String refreshToken) {
        log.info("刷新访问令牌");

        // 检查token类型
        String tokenType = jwtUtils.getTokenTypeFromToken(refreshToken);
        if (!"refresh".equals(tokenType)) {
            log.warn("刷新令牌失败: 期望refresh token，实际为{}", tokenType);
            throw new BizException(BizCodeEnum.REFRESH_TOKEN_INVALID, "Invalid token type for refresh");
        }

        // 从刷新令牌中获取用户信息（先获取用户信息以便进行登出时间检查）
        UserInfoDTO userInfo = jwtUtils.getUserInfoFromToken(refreshToken);
        if (userInfo == null) {
            log.warn("无法从刷新令牌中获取用户信息");
            throw new BizException(BizCodeEnum.REFRESH_TOKEN_INVALID);
        }

        // 验证刷新令牌（包含登出时间检查）
        if (!validateRefreshTokenWithLogoutCheck(refreshToken, userInfo.getUserId())) {
            log.warn("刷新令牌无效、已过期或已登出: userId={}", userInfo.getUserId());
            throw new BizException(BizCodeEnum.REFRESH_TOKEN_INVALID);
        }

        // 生成新的令牌对
        return generateAuthToken(userInfo);
    }



    @Override
    public boolean validateAccessToken(String accessToken) {
        if (accessToken == null || accessToken.trim().isEmpty()) {
            return false;
        }

        // 检查token类型是否为access token
        String tokenType = jwtUtils.getTokenTypeFromToken(accessToken);
        if (!"access".equals(tokenType)) {
            log.warn("Token类型错误: 期望access token，实际为{}", tokenType);
            return false;
        }

        return jwtUtils.validateToken(accessToken);
    }

    @Override
    public boolean validateAccessTokenWithLogoutCheck(String accessToken, String userId) {
        return validateTokenWithLogoutCheck(accessToken, userId, "Access Token");
    }

    @Override
    public boolean validateRefreshToken(String refreshToken) {
        if (refreshToken == null || refreshToken.trim().isEmpty()) {
            return false;
        }

        // 检查token类型是否为refresh token
        String tokenType = jwtUtils.getTokenTypeFromToken(refreshToken);
        if (!"refresh".equals(tokenType)) {
            log.warn("Token类型错误: 期望refresh token，实际为{}", tokenType);
            return false;
        }

        return jwtUtils.validateToken(refreshToken);
    }

    @Override
    public boolean validateRefreshTokenWithLogoutCheck(String refreshToken, String userId) {
        return validateTokenWithLogoutCheck(refreshToken, userId, "Refresh Token");
    }

    @Override
    public UserInfoDTO getUserInfoFromAccessToken(String accessToken) {
        // 先检查token类型，再进行完整验证
        String tokenType = jwtUtils.getTokenTypeFromToken(accessToken);
        if (!"access".equals(tokenType)) {
            log.warn("获取用户信息失败: 期望access token，实际为{}", tokenType);
            return null;
        }

        if (!validateAccessToken(accessToken)) {
            return null;
        }
        return jwtUtils.getUserInfoFromToken(accessToken);
    }

    @Override
    public boolean shouldRefreshAccessToken(String accessToken) {
        // 先检查token类型
        String tokenType = jwtUtils.getTokenTypeFromToken(accessToken);
        if (!"access".equals(tokenType)) {
            log.warn("检查刷新需求失败: 期望access token，实际为{}", tokenType);
            return false;
        }

        if (!validateAccessToken(accessToken)) {
            return false;
        }
        return jwtUtils.shouldRefreshToken(accessToken);
    }

    @Override
    public Map<String, Object> logout(String accessToken, String userId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查token类型
            String tokenType = jwtUtils.getTokenTypeFromToken(accessToken);
            if (!"access".equals(tokenType)) {
                log.warn("登出失败: 期望access token，实际为{}, userId={}", tokenType, userId);
                throw new BizException(BizCodeEnum.UNAUTHORIZED, "Invalid token type for logout");
            }

            // 验证access token
            if (accessToken == null || !jwtUtils.validateToken(accessToken)) {
                log.warn("登出失败: 无效的access token, userId={}", userId);
                throw new BizException(BizCodeEnum.UNAUTHORIZED, "Invalid access token");
            }

            // 验证token是否属于当前用户
            UserInfoDTO tokenUser = jwtUtils.getUserInfoFromToken(accessToken);
            if (tokenUser == null || !userId.equals(tokenUser.getUserId())) {
                log.warn("登出失败: token不属于当前用户, userId={}, tokenUserId={}", userId, tokenUser != null ? tokenUser.getUserId() : "null");
                throw new BizException(BizCodeEnum.UNAUTHORIZED, "Token does not belong to current user");
            }

            // 获取登出时间
            Instant logoutTime = Instant.now();

            // 更新用户的最后登出时间，使所有token失效
            updateUserLastLogoutTime(userId, logoutTime);

            // 构建返回结果
            result.put("success", true);
            result.put("message", "All tokens have been revoked");
            result.put("userId", userId);
            result.put("logoutTime", logoutTime);
            result.put("instruction", "Please clear all stored tokens on client side");

            log.info("用户登出成功: userId={}, logoutTime={}", userId, logoutTime);

        } catch (Exception e) {
            log.error("用户登出失败: userId={}, error={}", userId, e.getMessage());
            result.put("success", false);
            result.put("error", e.getMessage());

            // 如果是业务异常，重新抛出
            if (e instanceof BizException) {
                throw e;
            }

            // 其他异常包装为业务异常
            throw new BizException(BizCodeEnum.LOGOUT_FAILED, "Logout failed: " + e.getMessage());
        }

        return result;
    }

    /**
     * 验证Token是否因登出而失效（通用方法）
     */
    private boolean validateTokenWithLogoutCheck(String token, String userId, String tokenType) {
        // 首先进行基本的token验证
        if (!jwtUtils.validateToken(token)) {
            log.debug("{}基本验证失败: userId={}", tokenType, userId);
            return false;
        }

        // 获取token中的登录时间
        Instant tokenLoginTime = jwtUtils.getLoginTimeFromToken(token);
        if (tokenLoginTime == null) {
            log.warn("{}中缺少登录时间信息: userId={}", tokenType, userId);
            return false;
        }

        // 获取用户的最后登出时间
        Instant userLastLogoutTime = getUserLastLogoutTime(userId);
        if (userLastLogoutTime == null) {
            // 用户从未登出过，token有效
            return true;
        }

        // 如果token的登录时间早于用户的最后登出时间，则token无效
        boolean isValid = tokenLoginTime.isAfter(userLastLogoutTime);
        if (!isValid) {
            log.debug("{}已失效: userId={}, tokenLoginTime={}, lastLogoutTime={}",
                    tokenType, userId, tokenLoginTime, userLastLogoutTime);
        }
        return isValid;
    }

    /**
     * 获取用户最后登出时间
     */
    private Instant getUserLastLogoutTime(String userId) {
        try {
            DynamoDbTable<UserDO> table = getUserTable();
            UserDO userDO = dynamoDBService.findByKey(table, userId).orElse(null);
            return userDO != null ? userDO.getLastLogoutTime() : null;
        } catch (Exception e) {
            log.warn("获取用户最后登出时间失败: userId={}, error={}", userId, e.getMessage());
            return null;
        }
    }

    /**
     * 更新用户最后登出时间
     */
    private void updateUserLastLogoutTime(String userId, Instant logoutTime) {
        try {
            DynamoDbTable<UserDO> table = getUserTable();
            UserDO userDO = dynamoDBService.findByKey(table, userId).orElse(null);
            if (userDO != null) {
                userDO.setLastLogoutTime(logoutTime);
                userDO.setUpdatedTime(Instant.now());
                dynamoDBService.save(table, userDO);
                log.info("更新用户最后登出时间: userId={}, logoutTime={}", userId, logoutTime);
            } else {
                log.warn("更新登出时间失败，用户不存在: userId={}", userId);
            }
        } catch (Exception e) {
            log.error("更新用户最后登出时间失败: userId={}, error={}", userId, e.getMessage());
        }
    }

    /**
     * 获取用户表
     */
    private DynamoDbTable<UserDO> getUserTable() {
        return dynamoDBService.getTable(UserDO.class, SystemConstants.TABLE_USER);
    }
}
