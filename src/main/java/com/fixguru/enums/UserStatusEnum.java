package com.fixguru.enums;

import lombok.Getter;

/**
 * 用户状态枚举
 * 定义用户账户的各种状态及其权限控制
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Getter
public enum UserStatusEnum {


    /**
     * 激活状态
     * - 正常活跃用户状态
     * - 拥有完整的系统权限
     * - 可以使用所有功能
     * - 默认的正常状态
     */
    ACTIVE("ACTIVE", "激活", true),

    /**
     * 锁定状态
     * - 因安全原因临时锁定
     * - 密码错误次数过多
     * - 可以通过重置密码解锁
     * - 暂时无法登录
     */
    LOCKED("LOCKED", "锁定", false),

    /**
     * 禁用状态
     * - 管理员主动禁用
     * - 违规用户的处罚状态
     * - 需要管理员手动解禁
     * - 完全无法使用系统
     */
    DISABLED("DISABLED", "禁用", false);

    private final String code;
    private final String description;
    private final boolean canLogin;

    UserStatusEnum(String code, String description, boolean canLogin) {
        this.code = code;
        this.description = description;
        this.canLogin = canLogin;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 用户状态枚举
     */
    public static UserStatusEnum fromCode(String code) {
        for (UserStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的用户状态代码: " + code);
    }

    /**
     * 检查是否可以进行状态转换
     *
     * @param targetStatus 目标状态
     * @return 是否可以转换
     */
    public boolean canTransitionTo(UserStatusEnum targetStatus) {
        switch (this) {

            case ACTIVE:
                // 激活状态可以转换为锁定、禁用
                return targetStatus == LOCKED || targetStatus == DISABLED;
            case LOCKED:
                // 锁定状态可以转换为激活、禁用
                return targetStatus == ACTIVE || targetStatus == DISABLED;
            case DISABLED:
                // 禁用状态只能由管理员转换为激活
                return targetStatus == ACTIVE;
            default:
                return false;
        }
    }

    /**
     * 获取状态转换的原因描述
     *
     * @param targetStatus 目标状态
     * @return 转换原因
     */
    public String getTransitionReason(UserStatusEnum targetStatus) {
        if (!canTransitionTo(targetStatus)) {
            return "不允许从" + this.description + "状态转换为" + targetStatus.description + "状态";
        }

        switch (targetStatus) {
            case ACTIVE:
                return "账户解锁/解禁";
            case LOCKED:
                return "账户安全锁定";
            case DISABLED:
                return "管理员禁用账户";
            default:
                return "状态变更";
        }
    }
}
