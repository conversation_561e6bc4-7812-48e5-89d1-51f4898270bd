package com.fixguru.dto.request;

import com.fixguru.common.BaseRequest;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户登录请求
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserLoginRequest extends BaseRequest {

    /**
     * 邮箱
     */
    @NotBlank(message = "Email is required")
    @Email(message = "Invalid email format")
    private String email;

    /**
     * 密码
     */
    @NotBlank(message = "Password is required")
    private String password;
}
