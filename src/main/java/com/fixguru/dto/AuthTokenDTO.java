package com.fixguru.dto;

import lombok.Data;

import java.time.Instant;

/**
 * 认证令牌DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class AuthTokenDTO {

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 访问令牌过期时间（秒）
     */
    private Long expiresIn;

    /**
     * 刷新令牌过期时间（秒）
     */
    private Long refreshExpiresIn;

    /**
     * 令牌颁发时间
     */
    private Instant issuedAt;

    /**
     * 用户信息
     */
    private UserInfoDTO userInfo;

    /**
     * 构造函数
     */
    public AuthTokenDTO() {
        this.issuedAt = Instant.now();
    }

    /**
     * 构造函数
     *
     * @param accessToken      访问令牌
     * @param refreshToken     刷新令牌
     * @param expiresIn        访问令牌过期时间
     * @param refreshExpiresIn 刷新令牌过期时间
     */
    public AuthTokenDTO(String accessToken, String refreshToken, Long expiresIn, Long refreshExpiresIn) {
        this();
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        this.expiresIn = expiresIn;
        this.refreshExpiresIn = refreshExpiresIn;
    }
}
