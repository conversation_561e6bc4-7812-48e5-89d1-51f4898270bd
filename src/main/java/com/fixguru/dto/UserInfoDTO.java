package com.fixguru.dto;

import com.fixguru.enums.UserStatusEnum;
import lombok.Data;

import java.time.Instant;

/**
 * 用户信息DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class UserInfoDTO {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 用户状态
     */
    private UserStatusEnum status;

    /**
     * 最后登录时间
     */
    private Instant lastLoginTime;

    /**
     * 创建时间
     */
    private Instant createdTime;

    /**
     * 更新时间
     */
    private Instant updatedTime;
}
