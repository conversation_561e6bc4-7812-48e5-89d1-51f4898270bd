package com.fixguru.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 分页结果封装类
 * 企业级分页数据统一格式
 * 
 * @param <T> 数据类型
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageResult<T> {

    /**
     * 当前页码（从1开始）
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 总页数
     */
    private Integer pages;

    /**
     * 当前页数据
     */
    private List<T> records;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;

    /**
     * 是否为第一页
     */
    private Boolean isFirst;

    /**
     * 是否为最后一页
     */
    private Boolean isLast;

    public PageResult() {
    }

    public PageResult(Integer pageNum, Integer pageSize, Long total, List<T> records) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.records = records;
        this.calculatePages();
    }

    /**
     * 计算分页信息
     */
    private void calculatePages() {
        if (total != null && pageSize != null && pageSize > 0) {
            this.pages = (int) Math.ceil((double) total / pageSize);
        } else {
            this.pages = 0;
        }

        if (pageNum != null) {
            this.hasNext = pageNum < pages;
            this.hasPrevious = pageNum > 1;
            this.isFirst = pageNum == 1;
            this.isLast = pageNum.equals(pages);
        }
    }

    /**
     * 创建空分页结果
     */
    public static <T> PageResult<T> empty(Integer pageNum, Integer pageSize) {
        return new PageResult<>(pageNum, pageSize, 0L, List.of());
    }

    /**
     * 创建分页结果
     */
    public static <T> PageResult<T> of(Integer pageNum, Integer pageSize, Long total, List<T> records) {
        return new PageResult<>(pageNum, pageSize, total, records);
    }

    /**
     * 创建简单分页结果（不计算总数）
     */
    public static <T> PageResult<T> simple(Integer pageNum, Integer pageSize, List<T> records) {
        PageResult<T> result = new PageResult<>();
        result.pageNum = pageNum;
        result.pageSize = pageSize;
        result.records = records;
        result.hasNext = records != null && records.size() == pageSize;
        result.hasPrevious = pageNum > 1;
        result.isFirst = pageNum == 1;
        return result;
    }

    /**
     * 获取偏移量
     */
    public Integer getOffset() {
        if (pageNum == null || pageSize == null) {
            return 0;
        }
        return (pageNum - 1) * pageSize;
    }

    /**
     * 是否为空结果
     */
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }

    /**
     * 获取当前页记录数
     */
    public int getSize() {
        return records == null ? 0 : records.size();
    }
}
