package com.fixguru.common;

import lombok.Data;

/**
 * 基础请求类
 * 包含通用的请求字段
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public abstract class BaseRequest {

    /**
     * 请求ID（用于链路追踪）
     */
    private String requestId;

    /**
     * 客户端类型
     */
    private String clientType;

    /**
     * 客户端版本
     */
    private String clientVersion;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 用户ID（可选，用于需要用户身份的请求）
     */
    private String userId;
}
