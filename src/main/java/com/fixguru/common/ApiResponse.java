package com.fixguru.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fixguru.enums.BizCodeEnum;
import lombok.Data;

import java.time.Instant;

/**
 * 统一API响应格式
 *
 * @param <T> 响应数据类型
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {

    /**
     * 响应码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 响应时间戳
     */
    private Instant timestamp;

    /**
     * 请求ID（用于链路追踪）
     */
    private String requestId;

    public ApiResponse() {
        this.timestamp = Instant.now();
    }

    public ApiResponse(String code, String message) {
        this();
        this.code = code;
        this.message = message;
    }

    public ApiResponse(String code, String message, T data) {
        this(code, message);
        this.data = data;
    }

    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>("200", "操作成功");
    }

    /**
     * 成功响应（带数据）
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(BizCodeEnum.SUCCESS.getCode(), BizCodeEnum.SUCCESS.getMessage(), data);
    }

    /**
     * 成功响应（自定义消息）
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(BizCodeEnum.SUCCESS.getCode(), message, data);
    }

    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> error(String code, String message) {
        return new ApiResponse<>(code, message);
    }

    /**
     * 系统错误响应
     */
    public static <T> ApiResponse<T> systemError() {
        return new ApiResponse<>(BizCodeEnum.SYSTEM_ERROR.getCode(), BizCodeEnum.SYSTEM_ERROR.getMessage());
    }

    /**
     * 参数错误响应
     */
    public static <T> ApiResponse<T> paramError(String message) {
        return new ApiResponse<>(BizCodeEnum.INVALID_PARAMETER.getCode(), message);
    }

    /**
     * 未授权响应
     */
    public static <T> ApiResponse<T> unauthorized() {
        return new ApiResponse<>(BizCodeEnum.UNAUTHORIZED.getCode(), BizCodeEnum.UNAUTHORIZED.getMessage());
    }

    /**
     * 禁止访问响应
     */
    public static <T> ApiResponse<T> forbidden() {
        return new ApiResponse<>(BizCodeEnum.FORBIDDEN.getCode(), BizCodeEnum.FORBIDDEN.getMessage());
    }

    /**
     * 资源不存在响应
     */
    public static <T> ApiResponse<T> notFound() {
        return new ApiResponse<>(BizCodeEnum.RESOURCE_NOT_FOUND.getCode(), BizCodeEnum.RESOURCE_NOT_FOUND.getMessage());
    }

    /**
     * 资源不存在响应（自定义消息）
     */
    public static <T> ApiResponse<T> notFound(String message) {
        return new ApiResponse<>(BizCodeEnum.RESOURCE_NOT_FOUND.getCode(), message);
    }

    /**
     * 资源冲突响应
     */
    public static <T> ApiResponse<T> conflict(String message) {
        return new ApiResponse<>("409", message);
    }

    /**
     * 限流响应
     */
    public static <T> ApiResponse<T> rateLimited() {
        return new ApiResponse<>(BizCodeEnum.RATE_LIMIT_EXCEEDED.getCode(), BizCodeEnum.RATE_LIMIT_EXCEEDED.getMessage());
    }

    /**
     * 服务不可用响应
     */
    public static <T> ApiResponse<T> serviceUnavailable() {
        return new ApiResponse<>(BizCodeEnum.SERVICE_UNAVAILABLE.getCode(), BizCodeEnum.SERVICE_UNAVAILABLE.getMessage());
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return "200".equals(this.code);
    }

    /**
     * 设置请求ID
     */
    @SuppressWarnings("unchecked")
    public <R> ApiResponse<R> withRequestId(String requestId) {
        this.requestId = requestId;
        return (ApiResponse<R>) this;
    }

    /**
     * 创建分页响应
     */
    public static <T> ApiResponse<PageResult<T>> page(PageResult<T> pageResult) {
        return new ApiResponse<>("200", "查询成功", pageResult);
    }

    /**
     * 创建业务异常响应
     */
    public static <T> ApiResponse<T> business(String code, String message) {
        return new ApiResponse<>(code, message);
    }

    /**
     * 创建业务异常响应（带数据）
     */
    public static <T> ApiResponse<T> business(String code, String message, T data) {
        return new ApiResponse<>(code, message, data);
    }
}
