<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false">
    <!-- 禁用Logback内部状态日志 -->
    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />

    <!-- 注册自定义转换器 - 实现有值才显示的优雅效果 -->
    <conversionRule conversionWord="traceInfo" converterClass="com.fixguru.config.TraceInfoConverter" />

    <!-- 日志格式 - 使用自定义转换器，智能显示追踪信息 -->
    <property name="LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %traceInfo%msg%n"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>


    <!-- 日志级别配置 -->
    <!-- 系统组件日志级别 -->
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.apache.catalina" level="INFO"/>
    <logger name="org.apache.coyote" level="INFO"/>

    <!-- 屏蔽噪音日志 -->
    <logger name="com.amazonaws.serverless.proxy.spring" level="ERROR"/>
    <logger name="software.amazon.awssdk" level="WARN"/>
    <logger name="io.swagger" level="WARN"/>
    <logger name="ch.qos.logback" level="ERROR"/>
    <logger name="org.springframework.boot.logging" level="WARN"/>

    <!-- 应用业务日志 -->
    <logger name="com.fixguru" level="INFO"/>

    <!-- 根日志配置 - 只输出到控制台，适合Lambda和容器化部署 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>
