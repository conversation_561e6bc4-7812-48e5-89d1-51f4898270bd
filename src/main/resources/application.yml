# FixGuru 主配置文件
# 包含所有环境共享的配置

# 服务器基础配置（仅适用于本地开发，Lambda环境会忽略）
server:
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  shutdown: graceful

# Spring基础配置
spring:
  application:
    name: fixguru
  profiles:
    active: dev  # 默认使用开发环境
  jackson:
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  # 字符编码配置 - Spring Boot 3.x
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# Knife4j API文档配置 - 4.4.0版本
knife4j:
  enable: true
  production: false
  setting:
    language: zh_CN
    license:
      name: Apache License 2.0
      url: https://www.apache.org/licenses/LICENSE-2.0.html


# 应用基础配置（所有环境共享）
app:
  basic:
    version: v1.0.0
    timezone: UTC
    locale: en_US

  security:
    access-token-expiration-hours: 24  # 24小时 - 适合移动端长时间使用
    refresh-token-expiration-days: 30  # 30天 - 减少重新登录频率
    max-login-attempts: 5
    account-lock-minutes: 30
    cors-enabled: true

  file:
    allowed-types:
      - jpg
      - jpeg
      - png
      - gif
      - pdf

  monitor:
    enabled: true
    health-check-enabled: true
    retention-days: 7
    slow-query-threshold: 2000

  email:
    enabled: ${EMAIL_ENABLED:true}
    from-email: ${EMAIL_FROM:<EMAIL>}
    from-name: ${EMAIL_FROM_NAME:FixGuru}
    reset-token-expiration-minutes: 15
    reset-password-subject: Reset Your FixGuru Password
    reset-password-url: ${EMAIL_RESET_URL:https://fixguruapp.com/reset-password}
    send-rate-limit-minutes: 5
    daily-send-limit: 3
    smtp-host: ${SMTP_HOST:smtp.zoho.com}
    smtp-port: ${SMTP_PORT:587}
    smtp-username: ${SMTP_USERNAME:<EMAIL>}
    smtp-password: ${SMTP_PASSWORD:Dayu20250525}
    smtp-starttls-enable: true
    smtp-auth: true

# AWS基础配置
aws:
  region: ${AWS_REGION:us-west-1}

# 管理端点配置
management:
  endpoints:
    web:
      base-path: /actuator
  endpoint:
    health:
      probes:
        enabled: true

# 日志配置 - 详细配置在logback-spring.xml中
logging:
  # 日志级别和格式配置已移至logback-spring.xml，保持配置集中管理
