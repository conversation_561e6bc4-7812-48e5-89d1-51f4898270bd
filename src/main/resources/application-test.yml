# 测试环境配置 (AWS Lambda + DynamoDB)
# 继承 application.yml 的基础配置，只覆盖需要修改的部分
# 注意：Lambda环境不需要server.port配置，由AWS Lambda Runtime管理

# AWS配置 - 测试环境
aws:
  dynamodb:
    table-prefix: ${TABLE_PREFIX:test_}

# 应用配置 - 测试环境特定
app:
  basic:
    name: FixGuru Test

  security:
    jwt-secret: ${JWT_SECRET:FixGuru-Test-JWT-Secret-Key-2024}

  allowed-origins:
    - "https://test.fixguru.com"
    - "https://test-admin.fixguru.com"

# 日志配置 - 测试环境
logging:
  level:
    com.fixguru: INFO
    org.springframework: WARN
    software.amazon.awssdk: WARN

# Knife4j配置 - 测试环境（启用但有密码保护）
knife4j:
  basic:
    enable: true
    username: ${SWAGGER_USERNAME:admin}
    password: ${SWAGGER_PASSWORD:test123}
  info:
    title: FixGuru Test API
    description: FixGuru 测试环境 API

# Lambda优化配置
spring:
  main:
    lazy-initialization: true
