<?xml version="1.0" encoding="UTF-8"?>
<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.1.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.1.0 
          http://maven.apache.org/xsd/assembly-2.1.0.xsd">
    
    <id>lambda</id>
    <formats>
        <format>zip</format>
    </formats>
    
    <includeBaseDirectory>false</includeBaseDirectory>
    
    <files>
        <!-- Shaded JAR文件 -->
        <file>
            <source>${project.build.directory}/${project.artifactId}-${project.version}-lambda.jar</source>
            <outputDirectory>/</outputDirectory>
            <destName>app.jar</destName>
        </file>
    </files>
    
    <fileSets>
        <!-- 包含所有依赖库 -->
        <fileSet>
            <directory>${project.build.directory}/dependency</directory>
            <outputDirectory>/lib</outputDirectory>
            <includes>
                <include>**/*.jar</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>
