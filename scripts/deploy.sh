#!/bin/bash

# FixGuru 多环境部署脚本
# 支持 dev、test、prod 环境的一键部署

set -e

# 导入颜色工具
source "$(dirname "$0")/aws/colors.sh"

# 显示使用说明
show_usage() {
    print_info "FixGuru 多环境部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 <environment>"
    echo ""
    echo "支持的环境:"
    echo "  dev   - 开发环境 (AWS Lambda)"
    echo "  test  - 测试环境 (AWS Lambda)"
    echo "  prod  - 生产环境 (AWS Lambda)"
    echo ""
    echo "示例:"
    echo "  $0 dev    # 部署到开发环境"
    echo "  $0 test   # 部署到测试环境"
    echo "  $0 prod   # 部署到生产环境"
}

# 检查参数
if [ $# -eq 0 ]; then
    show_usage
    exit 1
fi

ENVIRONMENT=$1

# 验证环境参数
case $ENVIRONMENT in
    dev|test|prod)
        print_success "环境参数有效: $ENVIRONMENT"
        ;;
    *)
        print_error "无效的环境参数: $ENVIRONMENT"
        show_usage
        exit 1
        ;;
esac

# 设置环境变量
STACK_NAME="fixguru-$ENVIRONMENT"
REGION="${AWS_REGION:-us-west-1}"
DEPLOYMENT_BUCKET="${DEPLOYMENT_BUCKET:-fixguru-lambda-deployment-1753610168}"

print_info "🚀 开始部署 FixGuru 到 $ENVIRONMENT 环境..."
print_info "📍 堆栈名称: $STACK_NAME"
print_info "📍 区域: $REGION"
print_info "📍 部署桶: $DEPLOYMENT_BUCKET"
echo ""

# 构建项目
print_warning "🔨 构建项目..."
mvn clean package -DskipTests

# 构建SAM应用
print_warning "🔨 构建SAM应用..."
sam build

# 部署SAM应用
print_warning "🚀 部署到AWS..."
sam deploy \
    --stack-name $STACK_NAME \
    --region $REGION \
    --capabilities CAPABILITY_IAM \
    --parameter-overrides Environment=$ENVIRONMENT \
    --s3-bucket $DEPLOYMENT_BUCKET \
    --no-confirm-changeset

print_success "🎉 部署完成！"
echo ""

# 获取API Gateway URL
API_URL=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --region $REGION \
    --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayUrl`].OutputValue' \
    --output text 2>/dev/null || echo "未找到API Gateway URL")

if [ "$API_URL" != "未找到API Gateway URL" ]; then
    print_success "🌐 API Gateway URL: $API_URL"
    print_success "📚 API文档: ${API_URL}/doc.html"
    print_success "🔍 健康检查: ${API_URL}/api/v1/health"
fi

echo ""
print_info "📋 环境信息:"
print_info "  环境: $ENVIRONMENT"
print_info "  表前缀: ${ENVIRONMENT}_"
print_info "  DynamoDB表: ${ENVIRONMENT}_User"
