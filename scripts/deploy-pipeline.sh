#!/bin/bash

# FixGuru CodePipeline 部署脚本
# 基于现有项目结构创建CI/CD流水线

set -e

# 导入颜色工具
source "$(dirname "$0")/aws/colors.sh"

# 脚本信息
SCRIPT_NAME="FixGuru Pipeline Deployer"
VERSION="1.0.0"

print_header() {
    echo
    print_blue "=================================="
    print_blue "  $SCRIPT_NAME v$VERSION"
    print_blue "=================================="
    echo
}

print_usage() {
    echo "用法: $0 [ENVIRONMENT]"
    echo
    echo "参数:"
    echo "  ENVIRONMENT    目标环境 (dev/test/prod)"
    echo
    echo "示例:"
    echo "  $0 dev         # 部署开发环境流水线"
    echo "  $0 prod        # 部署生产环境流水线"
    echo
}

# 检查参数
if [ $# -eq 0 ]; then
    print_usage
    exit 1
fi

ENVIRONMENT=$1

# 验证环境参数
case $ENVIRONMENT in
    dev|test|prod)
        print_green "✓ 环境参数有效: $ENVIRONMENT"
        ;;
    *)
        print_red "✗ 无效的环境参数: $ENVIRONMENT"
        print_usage
        exit 1
        ;;
esac

print_header

# 检查必需的工具
print_blue "检查必需工具..."
command -v aws >/dev/null 2>&1 || { print_red "✗ AWS CLI 未安装"; exit 1; }
command -v git >/dev/null 2>&1 || { print_red "✗ Git 未安装"; exit 1; }
print_green "✓ 所有必需工具已安装"

# 检查AWS凭证
print_blue "检查AWS凭证..."
if ! aws sts get-caller-identity >/dev/null 2>&1; then
    print_red "✗ AWS凭证未配置或无效"
    exit 1
fi
print_green "✓ AWS凭证有效"

# 设置变量
PROJECT_NAME="fixguru"
AWS_REGION=${AWS_REGION:-"us-west-1"}
GITHUB_OWNER=${GITHUB_OWNER:-"DayuHZ"}
GITHUB_REPO=${GITHUB_REPO:-"FixGuru_Service"}
GITHUB_BRANCH=${GITHUB_BRANCH:-"master"}
DEPLOYMENT_BUCKET=${DEPLOYMENT_BUCKET:-"fixguru-lambda-deployment-1753610168"}

PIPELINE_STACK_NAME="${PROJECT_NAME}-pipeline-${ENVIRONMENT}"

print_blue "部署配置:"
echo "  项目名称: $PROJECT_NAME"
echo "  环境: $ENVIRONMENT"
echo "  AWS区域: $AWS_REGION"
echo "  GitHub仓库: $GITHUB_OWNER/$GITHUB_REPO"
echo "  GitHub分支: $GITHUB_BRANCH"
echo "  部署桶: $DEPLOYMENT_BUCKET"
echo "  流水线堆栈: $PIPELINE_STACK_NAME"
echo

# 检查GitHub Token
print_blue "检查GitHub访问令牌..."
if ! aws secretsmanager describe-secret --secret-id github-token --region $AWS_REGION >/dev/null 2>&1; then
    print_yellow "⚠ GitHub访问令牌未找到，需要创建"
    echo
    print_blue "请按以下步骤创建GitHub Personal Access Token:"
    echo "1. 访问: https://github.com/settings/tokens"
    echo "2. 点击 'Generate new token'"
    echo "3. 选择 'repo' 权限"
    echo "4. 复制生成的token"
    echo
    read -p "请输入GitHub Personal Access Token: " GITHUB_TOKEN
    
    if [ -z "$GITHUB_TOKEN" ]; then
        print_red "✗ GitHub Token不能为空"
        exit 1
    fi
    
    print_blue "创建GitHub Token密钥..."
    aws secretsmanager create-secret \
        --name github-token \
        --description "GitHub Personal Access Token for CodePipeline" \
        --secret-string "{\"token\":\"$GITHUB_TOKEN\"}" \
        --region $AWS_REGION
    
    print_green "✓ GitHub Token已存储到Secrets Manager"
else
    print_green "✓ GitHub访问令牌已存在"
fi

# 检查JWT Secret
print_blue "检查JWT密钥..."
if ! aws ssm get-parameter --name "/fixguru/jwt-secret" --region $AWS_REGION >/dev/null 2>&1; then
    print_yellow "⚠ JWT密钥未找到，正在创建..."
    
    # 生成JWT密钥
    JWT_SECRET=$(openssl rand -base64 32)
    
    aws ssm put-parameter \
        --name "/fixguru/jwt-secret" \
        --description "JWT Secret for FixGuru application" \
        --value "$JWT_SECRET" \
        --type "SecureString" \
        --region $AWS_REGION
    
    print_green "✓ JWT密钥已创建并存储到Parameter Store"
else
    print_green "✓ JWT密钥已存在"
fi

# 部署Pipeline
print_blue "部署CodePipeline..."
aws cloudformation deploy \
    --template-file pipeline-template.yaml \
    --stack-name $PIPELINE_STACK_NAME \
    --parameter-overrides \
        ProjectName=$PROJECT_NAME \
        Environment=$ENVIRONMENT \
        GitHubOwner=$GITHUB_OWNER \
        GitHubRepo=$GITHUB_REPO \
        GitHubBranch=$GITHUB_BRANCH \
        DeploymentBucket=$DEPLOYMENT_BUCKET \
    --capabilities CAPABILITY_NAMED_IAM \
    --region $AWS_REGION

if [ $? -eq 0 ]; then
    print_green "✓ CodePipeline部署成功!"
    
    # 获取输出信息
    PIPELINE_NAME=$(aws cloudformation describe-stacks \
        --stack-name $PIPELINE_STACK_NAME \
        --region $AWS_REGION \
        --query 'Stacks[0].Outputs[?OutputKey==`PipelineName`].OutputValue' \
        --output text)
    
    PIPELINE_URL=$(aws cloudformation describe-stacks \
        --stack-name $PIPELINE_STACK_NAME \
        --region $AWS_REGION \
        --query 'Stacks[0].Outputs[?OutputKey==`PipelineUrl`].OutputValue' \
        --output text)
    
    echo
    print_green "🎉 部署完成!"
    echo "  Pipeline名称: $PIPELINE_NAME"
    echo "  Pipeline URL: $PIPELINE_URL"
    echo
    print_blue "下一步:"
    echo "1. 推送代码到GitHub触发首次构建"
    echo "2. 在AWS控制台监控Pipeline执行"
    echo "3. 验证应用部署结果"
    
else
    print_red "✗ CodePipeline部署失败"
    exit 1
fi
