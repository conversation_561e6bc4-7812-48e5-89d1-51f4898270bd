#!/bin/bash

# 通用颜色定义文件
# 为所有脚本提供统一的颜色常量

# 基础颜色
export RED='\033[0;31m'
export GREEN='\033[0;32m'
export YELLOW='\033[1;33m'
export BLUE='\033[0;34m'
export PURPLE='\033[0;35m'
export CYAN='\033[0;36m'
export WHITE='\033[1;37m'
export NC='\033[0m' # No Color

# 背景颜色
export BG_RED='\033[41m'
export BG_GREEN='\033[42m'
export BG_YELLOW='\033[43m'
export BG_BLUE='\033[44m'
export BG_PURPLE='\033[45m'
export BG_CYAN='\033[46m'

# 文本样式
export BOLD='\033[1m'
export DIM='\033[2m'
export UNDERLINE='\033[4m'
export BLINK='\033[5m'
export REVERSE='\033[7m'

# 常用组合
export SUCCESS="${GREEN}✅${NC}"
export ERROR="${RED}❌${NC}"
export WARNING="${YELLOW}⚠️${NC}"
export INFO="${BLUE}ℹ️${NC}"
export LOADING="${YELLOW}⏳${NC}"
export ROCKET="${BLUE}🚀${NC}"
export GEAR="${BLUE}🔧${NC}"
export FOLDER="${BLUE}📁${NC}"
export FILE="${BLUE}📄${NC}"

# 打印函数
print_success() {
    echo -e "${SUCCESS} $1"
}

print_error() {
    echo -e "${ERROR} $1"
}

print_warning() {
    echo -e "${WARNING} $1"
}

print_info() {
    echo -e "${INFO} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

print_section() {
    echo -e "${YELLOW}$1${NC}"
}

# 兼容性函数 - 为了支持现有脚本
print_red() {
    echo -e "${RED}$1${NC}"
}

print_green() {
    echo -e "${GREEN}$1${NC}"
}

print_yellow() {
    echo -e "${YELLOW}$1${NC}"
}

print_blue() {
    echo -e "${BLUE}$1${NC}"
}

print_purple() {
    echo -e "${PURPLE}$1${NC}"
}

print_cyan() {
    echo -e "${CYAN}$1${NC}"
}

# AWS通用检查函数
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI未安装"
        print_warning "安装命令: brew install awscli"
        return 1
    fi
    print_success "AWS CLI检查通过"
    return 0
}

check_aws_credentials() {
    if ! aws sts get-caller-identity > /dev/null 2>&1; then
        print_error "AWS凭证未配置"
        print_warning "请运行: aws configure"
        return 1
    fi

    local caller_identity=$(aws sts get-caller-identity --query 'Account' --output text)
    print_success "AWS凭证检查通过 (账户: $caller_identity)"
    return 0
}

check_aws_environment() {
    check_aws_cli || return 1
    check_aws_credentials || return 1
    return 0
}

# 如果脚本被直接执行，显示颜色示例
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo -e "${BLUE}🎨 颜色定义文件${NC}"
    echo ""
    echo "基础颜色示例："
    echo -e "${RED}红色文本${NC}"
    echo -e "${GREEN}绿色文本${NC}"
    echo -e "${YELLOW}黄色文本${NC}"
    echo -e "${BLUE}蓝色文本${NC}"
    echo -e "${PURPLE}紫色文本${NC}"
    echo -e "${CYAN}青色文本${NC}"
    echo ""
    echo "常用符号："
    print_success "成功消息"
    print_error "错误消息"
    print_warning "警告消息"
    print_info "信息消息"
    echo ""
    echo "AWS检查函数："
    echo "  check_aws_cli"
    echo "  check_aws_credentials"
    echo "  check_aws_environment"
    echo ""
    echo "使用方法："
    echo "  source scripts/aws/colors.sh"
    echo "  print_success \"操作成功\""
fi
