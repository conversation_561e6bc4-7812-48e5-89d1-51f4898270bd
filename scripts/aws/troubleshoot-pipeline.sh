#!/bin/bash

# FixGuru Pipeline 故障排除脚本
# 自动诊断Pipeline问题并提供解决建议

set -e

# 导入颜色工具
source "$(dirname "$0")/colors.sh"

# 脚本信息
SCRIPT_NAME="FixGuru Pipeline Troubleshooter"
VERSION="1.0.0"

print_header() {
    echo
    print_blue "=================================="
    print_blue "  $SCRIPT_NAME v$VERSION"
    print_blue "=================================="
    echo
}

# 检查参数
ENVIRONMENT=${1:-"dev"}

# 验证环境参数
case $ENVIRONMENT in
    dev|test|prod)
        ;;
    *)
        print_red "✗ 无效的环境参数: $ENVIRONMENT"
        echo "用法: $0 [dev|test|prod]"
        exit 1
        ;;
esac

# 设置变量
PROJECT_NAME="fixguru"
AWS_REGION=${AWS_REGION:-"us-west-1"}
PIPELINE_NAME="${PROJECT_NAME}-pipeline-${ENVIRONMENT}"
BUILD_PROJECT="${PROJECT_NAME}-build-${ENVIRONMENT}"
APP_STACK="${PROJECT_NAME}-app-${ENVIRONMENT}"

print_header
print_blue "诊断环境: $ENVIRONMENT"
echo

# 检查AWS凭证
print_blue "1. 检查AWS凭证..."
if aws sts get-caller-identity >/dev/null 2>&1; then
    ACCOUNT_ID=$(aws sts get-caller-identity --query 'Account' --output text)
    USER_ARN=$(aws sts get-caller-identity --query 'Arn' --output text)
    print_green "✅ AWS凭证有效"
    echo "   账户ID: $ACCOUNT_ID"
    echo "   用户: $USER_ARN"
else
    print_red "❌ AWS凭证无效或未配置"
    echo "   解决方案: 运行 'aws configure' 配置凭证"
    exit 1
fi
echo

# 检查Pipeline是否存在
print_blue "2. 检查Pipeline是否存在..."
if aws codepipeline get-pipeline --name $PIPELINE_NAME --region $AWS_REGION >/dev/null 2>&1; then
    print_green "✅ Pipeline存在: $PIPELINE_NAME"
else
    print_red "❌ Pipeline不存在: $PIPELINE_NAME"
    echo "   解决方案: 运行 './scripts/deploy-pipeline.sh $ENVIRONMENT' 创建Pipeline"
    exit 1
fi
echo

# 检查GitHub Token
print_blue "3. 检查GitHub访问令牌..."
if aws secretsmanager describe-secret --secret-id github-token --region $AWS_REGION >/dev/null 2>&1; then
    print_green "✅ GitHub Token存在"
    
    # 测试Token是否有效（通过检查最近的Pipeline执行）
    LATEST_SOURCE_STATUS=$(aws codepipeline get-pipeline-state --name $PIPELINE_NAME --region $AWS_REGION --query 'stageStates[?stageName==`Source`].latestExecution.status' --output text)
    
    if [ "$LATEST_SOURCE_STATUS" = "Failed" ]; then
        print_yellow "⚠️  Source阶段失败，可能是Token问题"
        echo "   解决方案: 更新GitHub Token"
        echo "   命令: aws secretsmanager update-secret --secret-id github-token --secret-string '{\"token\":\"NEW_TOKEN\"}' --region $AWS_REGION"
    fi
else
    print_red "❌ GitHub Token不存在"
    echo "   解决方案: 创建GitHub Token"
    echo "   命令: aws secretsmanager create-secret --name github-token --secret-string '{\"token\":\"YOUR_TOKEN\"}' --region $AWS_REGION"
fi
echo

# 检查JWT密钥
print_blue "4. 检查JWT密钥..."
if aws ssm get-parameter --name "/fixguru/jwt-secret" --region $AWS_REGION >/dev/null 2>&1; then
    print_green "✅ JWT密钥存在"
else
    print_yellow "⚠️  JWT密钥不存在"
    echo "   解决方案: 创建JWT密钥"
    echo "   命令: aws ssm put-parameter --name '/fixguru/jwt-secret' --value 'YOUR_JWT_SECRET' --type SecureString --region $AWS_REGION"
fi
echo

# 检查最新Pipeline执行状态
print_blue "5. 检查最新Pipeline执行..."
LATEST_EXECUTION=$(aws codepipeline list-pipeline-executions --pipeline-name $PIPELINE_NAME --region $AWS_REGION --max-items 1 --query 'pipelineExecutionSummaries[0]')

if [ "$LATEST_EXECUTION" != "null" ]; then
    EXEC_STATUS=$(echo "$LATEST_EXECUTION" | jq -r '.status')
    EXEC_ID=$(echo "$LATEST_EXECUTION" | jq -r '.pipelineExecutionId')
    
    case $EXEC_STATUS in
        "Succeeded")
            print_green "✅ 最新执行成功"
            ;;
        "Failed")
            print_red "❌ 最新执行失败"
            echo "   执行ID: $EXEC_ID"
            echo "   详细诊断正在进行..."
            
            # 检查失败的阶段
            FAILED_STAGES=$(aws codepipeline get-pipeline-state --name $PIPELINE_NAME --region $AWS_REGION --query 'stageStates[?latestExecution.status==`Failed`].stageName' --output text)
            
            if [[ $FAILED_STAGES == *"Source"* ]]; then
                print_red "   ❌ Source阶段失败"
                echo "      可能原因: GitHub Token无效、仓库权限问题"
                echo "      解决方案: 检查GitHub Token和仓库访问权限"
            fi
            
            if [[ $FAILED_STAGES == *"Build"* ]]; then
                print_red "   ❌ Build阶段失败"
                echo "      可能原因: 编译错误、测试失败、依赖问题"
                echo "      解决方案: 查看构建日志"
                echo "      命令: ./scripts/aws/monitor-pipeline.sh $ENVIRONMENT --build-logs"
            fi
            
            if [[ $FAILED_STAGES == *"Deploy"* ]]; then
                print_red "   ❌ Deploy阶段失败"
                echo "      可能原因: CloudFormation模板错误、权限不足"
                echo "      解决方案: 查看CloudFormation事件"
                echo "      命令: ./scripts/aws/monitor-pipeline.sh $ENVIRONMENT --deploy-logs"
            fi
            ;;
        "InProgress")
            print_yellow "⏳ Pipeline正在执行中"
            echo "   执行ID: $EXEC_ID"
            echo "   监控命令: ./scripts/aws/monitor-pipeline.sh $ENVIRONMENT --follow"
            ;;
        *)
            print_yellow "⏸️  Pipeline状态: $EXEC_STATUS"
            ;;
    esac
else
    print_yellow "⚠️  没有找到Pipeline执行记录"
    echo "   解决方案: 手动触发Pipeline"
    echo "   命令: ./scripts/aws/manage-pipeline.sh start $ENVIRONMENT"
fi
echo

# 检查CodeBuild项目
print_blue "6. 检查CodeBuild项目..."
if aws codebuild batch-get-projects --names $BUILD_PROJECT --region $AWS_REGION >/dev/null 2>&1; then
    print_green "✅ CodeBuild项目存在: $BUILD_PROJECT"
    
    # 检查最新构建状态
    BUILD_ID=$(aws codebuild list-builds-for-project --project-name $BUILD_PROJECT --region $AWS_REGION --query 'ids[0]' --output text 2>/dev/null)
    
    if [ "$BUILD_ID" != "None" ] && [ -n "$BUILD_ID" ]; then
        BUILD_STATUS=$(aws codebuild batch-get-builds --ids $BUILD_ID --region $AWS_REGION --query 'builds[0].buildStatus' --output text)
        
        case $BUILD_STATUS in
            "SUCCEEDED")
                print_green "   ✅ 最新构建成功"
                ;;
            "FAILED")
                print_red "   ❌ 最新构建失败"
                echo "      构建ID: $BUILD_ID"
                echo "      查看日志: ./scripts/aws/monitor-pipeline.sh $ENVIRONMENT --build-logs"
                ;;
            "IN_PROGRESS")
                print_yellow "   ⏳ 构建进行中"
                ;;
        esac
    fi
else
    print_red "❌ CodeBuild项目不存在: $BUILD_PROJECT"
    echo "   解决方案: 重新部署Pipeline"
    echo "   命令: ./scripts/deploy-pipeline.sh $ENVIRONMENT"
fi
echo

# 检查应用堆栈
print_blue "7. 检查应用堆栈..."
if aws cloudformation describe-stacks --stack-name $APP_STACK --region $AWS_REGION >/dev/null 2>&1; then
    STACK_STATUS=$(aws cloudformation describe-stacks --stack-name $APP_STACK --region $AWS_REGION --query 'Stacks[0].StackStatus' --output text)
    
    case $STACK_STATUS in
        *"COMPLETE")
            print_green "✅ 应用堆栈状态正常: $STACK_STATUS"
            ;;
        *"FAILED")
            print_red "❌ 应用堆栈失败: $STACK_STATUS"
            echo "   查看事件: ./scripts/aws/monitor-pipeline.sh $ENVIRONMENT --deploy-logs"
            ;;
        *"IN_PROGRESS")
            print_yellow "⏳ 应用堆栈更新中: $STACK_STATUS"
            ;;
    esac
else
    print_yellow "⚠️  应用堆栈不存在: $APP_STACK"
    echo "   这是正常的，如果Pipeline从未成功执行过"
fi
echo

# 检查Lambda函数
print_blue "8. 检查Lambda函数..."
LAMBDA_FUNCTION="${PROJECT_NAME}-function-${ENVIRONMENT}"
if aws lambda get-function --function-name $LAMBDA_FUNCTION --region $AWS_REGION >/dev/null 2>&1; then
    print_green "✅ Lambda函数存在: $LAMBDA_FUNCTION"
    
    # 测试Lambda函数
    print_blue "   测试Lambda函数..."
    if aws lambda invoke --function-name $LAMBDA_FUNCTION --region $AWS_REGION /tmp/lambda-test-response.json >/dev/null 2>&1; then
        print_green "   ✅ Lambda函数可以调用"
    else
        print_red "   ❌ Lambda函数调用失败"
        echo "      查看日志: aws logs tail /aws/lambda/$LAMBDA_FUNCTION --region $AWS_REGION"
    fi
else
    print_yellow "⚠️  Lambda函数不存在: $LAMBDA_FUNCTION"
    echo "   这是正常的，如果Pipeline从未成功部署过"
fi
echo

# 提供总结和建议
print_blue "📋 诊断总结和建议:"
echo

# 检查是否有失败的Pipeline执行
if [ "$EXEC_STATUS" = "Failed" ]; then
    print_red "🚨 发现问题: Pipeline执行失败"
    echo
    print_blue "建议的排查步骤:"
    echo "1. 查看详细状态: ./scripts/aws/manage-pipeline.sh status $ENVIRONMENT"
    echo "2. 查看构建日志: ./scripts/aws/monitor-pipeline.sh $ENVIRONMENT --build-logs"
    echo "3. 查看部署日志: ./scripts/aws/monitor-pipeline.sh $ENVIRONMENT --deploy-logs"
    echo "4. 手动重试: ./scripts/aws/manage-pipeline.sh start $ENVIRONMENT"
    echo
elif [ "$EXEC_STATUS" = "Succeeded" ]; then
    print_green "✅ Pipeline运行正常"
    echo
    print_blue "可用的管理命令:"
    echo "• 查看状态: ./scripts/aws/manage-pipeline.sh status $ENVIRONMENT"
    echo "• 手动触发: ./scripts/aws/manage-pipeline.sh start $ENVIRONMENT"
    echo "• 实时监控: ./scripts/aws/monitor-pipeline.sh $ENVIRONMENT --follow"
    echo
else
    print_yellow "⚠️  Pipeline可能需要首次执行"
    echo
    print_blue "建议操作:"
    echo "1. 手动触发Pipeline: ./scripts/aws/manage-pipeline.sh start $ENVIRONMENT"
    echo "2. 实时监控执行: ./scripts/aws/monitor-pipeline.sh $ENVIRONMENT --follow"
    echo
fi

print_blue "📚 相关文档:"
echo "• CI/CD部署指南: docs/CICD部署指南.md"
echo "• AWS部署指南: docs/AWS部署与开发指南.md"
echo "• Pipeline控制台: https://console.aws.amazon.com/codesuite/codepipeline/pipelines/$PIPELINE_NAME/view"
