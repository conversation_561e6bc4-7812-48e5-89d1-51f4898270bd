#!/bin/bash

# FixGuru Pipeline 实时监控脚本
# 提供Pipeline执行的实时监控和详细状态显示

set -e

# 导入颜色工具
source "$(dirname "$0")/colors.sh"

# 脚本信息
SCRIPT_NAME="FixGuru Pipeline Monitor"
VERSION="1.0.0"

print_header() {
    clear
    echo
    print_blue "=================================="
    print_blue "  $SCRIPT_NAME v$VERSION"
    print_blue "=================================="
    echo
}

print_usage() {
    echo "用法: $0 [ENVIRONMENT] [OPTIONS]"
    echo
    echo "参数:"
    echo "  ENVIRONMENT    目标环境 (dev/test/prod)"
    echo
    echo "选项:"
    echo "  --follow       实时跟踪Pipeline执行"
    echo "  --build-logs   显示构建日志"
    echo "  --deploy-logs  显示部署日志"
    echo
    echo "示例:"
    echo "  $0 dev                    # 显示开发环境Pipeline状态"
    echo "  $0 dev --follow           # 实时跟踪开发环境Pipeline"
    echo "  $0 dev --build-logs       # 显示构建日志"
    echo
}

# 检查参数
ENVIRONMENT=${1:-"dev"}
FOLLOW_MODE=false
BUILD_LOGS=false
DEPLOY_LOGS=false

# 解析选项
for arg in "$@"; do
    case $arg in
        --follow)
            FOLLOW_MODE=true
            shift
            ;;
        --build-logs)
            BUILD_LOGS=true
            shift
            ;;
        --deploy-logs)
            DEPLOY_LOGS=true
            shift
            ;;
    esac
done

# 验证环境参数
case $ENVIRONMENT in
    dev|test|prod)
        ;;
    *)
        print_red "✗ 无效的环境参数: $ENVIRONMENT"
        print_usage
        exit 1
        ;;
esac

# 设置变量
PROJECT_NAME="fixguru"
AWS_REGION=${AWS_REGION:-"us-west-1"}
PIPELINE_NAME="${PROJECT_NAME}-pipeline-${ENVIRONMENT}"
BUILD_PROJECT="${PROJECT_NAME}-build-${ENVIRONMENT}"
APP_STACK="${PROJECT_NAME}-app-${ENVIRONMENT}"

# 检查AWS凭证
if ! aws sts get-caller-identity >/dev/null 2>&1; then
    print_red "✗ AWS凭证未配置或无效"
    exit 1
fi

# 检查Pipeline是否存在
if ! aws codepipeline get-pipeline --name $PIPELINE_NAME --region $AWS_REGION >/dev/null 2>&1; then
    print_red "✗ Pipeline不存在: $PIPELINE_NAME"
    exit 1
fi

# 显示Pipeline基本信息
show_pipeline_info() {
    print_header
    print_blue "Pipeline信息:"
    echo "  名称: $PIPELINE_NAME"
    echo "  环境: $ENVIRONMENT"
    echo "  区域: $AWS_REGION"
    echo "  控制台: https://console.aws.amazon.com/codesuite/codepipeline/pipelines/$PIPELINE_NAME/view"
    echo
}

# 显示Pipeline状态
show_pipeline_status() {
    print_blue "Pipeline状态:"
    
    # 获取Pipeline状态
    PIPELINE_STATE=$(aws codepipeline get-pipeline-state --name $PIPELINE_NAME --region $AWS_REGION)
    
    # 显示各阶段状态
    echo "$PIPELINE_STATE" | jq -r '.stageStates[] | 
        if .latestExecution.status == "Succeeded" then
            "  ✅ \(.stageName): \(.latestExecution.status)"
        elif .latestExecution.status == "InProgress" then
            "  ⏳ \(.stageName): \(.latestExecution.status)"
        elif .latestExecution.status == "Failed" then
            "  ❌ \(.stageName): \(.latestExecution.status)"
        else
            "  ⏸️  \(.stageName): \(.latestExecution.status // "未执行")"
        end'
    
    echo
    
    # 显示最新执行信息
    LATEST_EXECUTION=$(aws codepipeline list-pipeline-executions --pipeline-name $PIPELINE_NAME --region $AWS_REGION --max-items 1 --query 'pipelineExecutionSummaries[0]')
    
    if [ "$LATEST_EXECUTION" != "null" ]; then
        print_blue "最新执行:"
        EXEC_STATUS=$(echo "$LATEST_EXECUTION" | jq -r '.status')
        EXEC_ID=$(echo "$LATEST_EXECUTION" | jq -r '.pipelineExecutionId')
        START_TIME=$(echo "$LATEST_EXECUTION" | jq -r '.startTime')
        
        case $EXEC_STATUS in
            "Succeeded")
                echo "  ✅ 状态: $EXEC_STATUS"
                ;;
            "InProgress")
                echo "  ⏳ 状态: $EXEC_STATUS"
                ;;
            "Failed")
                echo "  ❌ 状态: $EXEC_STATUS"
                ;;
            *)
                echo "  ⏸️  状态: $EXEC_STATUS"
                ;;
        esac
        
        echo "  🆔 执行ID: $EXEC_ID"
        echo "  ⏰ 开始时间: $START_TIME"
    fi
    
    echo
}

# 显示构建日志
show_build_logs() {
    print_blue "构建日志 (最新):"
    
    # 获取最新构建ID
    BUILD_ID=$(aws codebuild list-builds-for-project --project-name $BUILD_PROJECT --region $AWS_REGION --query 'ids[0]' --output text 2>/dev/null)
    
    if [ "$BUILD_ID" = "None" ] || [ -z "$BUILD_ID" ]; then
        print_yellow "⚠ 没有找到构建记录"
        return
    fi
    
    echo "构建ID: $BUILD_ID"
    echo "日志组: /aws/codebuild/$BUILD_PROJECT"
    echo
    
    # 显示构建状态
    BUILD_INFO=$(aws codebuild batch-get-builds --ids $BUILD_ID --region $AWS_REGION --query 'builds[0]')
    BUILD_STATUS=$(echo "$BUILD_INFO" | jq -r '.buildStatus')
    CURRENT_PHASE=$(echo "$BUILD_INFO" | jq -r '.currentPhase // "未知"')
    
    case $BUILD_STATUS in
        "SUCCEEDED")
            echo "✅ 构建状态: $BUILD_STATUS"
            ;;
        "IN_PROGRESS")
            echo "⏳ 构建状态: $BUILD_STATUS (当前阶段: $CURRENT_PHASE)"
            ;;
        "FAILED")
            echo "❌ 构建状态: $BUILD_STATUS"
            ;;
        *)
            echo "⏸️  构建状态: $BUILD_STATUS"
            ;;
    esac
    
    echo
    print_blue "构建日志内容:"
    echo "----------------------------------------"
    
    # 显示日志内容
    aws logs get-log-events \
        --log-group-name "/aws/codebuild/$BUILD_PROJECT" \
        --log-stream-name "$BUILD_ID" \
        --region $AWS_REGION \
        --query 'events[].message' \
        --output text 2>/dev/null | tail -50
}

# 显示部署日志
show_deploy_logs() {
    print_blue "部署日志 (CloudFormation事件):"
    
    # 检查堆栈是否存在
    if ! aws cloudformation describe-stacks --stack-name $APP_STACK --region $AWS_REGION >/dev/null 2>&1; then
        print_yellow "⚠ 应用堆栈不存在: $APP_STACK"
        return
    fi
    
    # 显示堆栈状态
    STACK_STATUS=$(aws cloudformation describe-stacks --stack-name $APP_STACK --region $AWS_REGION --query 'Stacks[0].StackStatus' --output text)
    
    case $STACK_STATUS in
        *"COMPLETE")
            echo "✅ 堆栈状态: $STACK_STATUS"
            ;;
        *"IN_PROGRESS")
            echo "⏳ 堆栈状态: $STACK_STATUS"
            ;;
        *"FAILED")
            echo "❌ 堆栈状态: $STACK_STATUS"
            ;;
        *)
            echo "⏸️  堆栈状态: $STACK_STATUS"
            ;;
    esac
    
    echo
    print_blue "最近的堆栈事件:"
    echo "----------------------------------------"
    
    # 显示最近的堆栈事件
    aws cloudformation describe-stack-events \
        --stack-name $APP_STACK \
        --region $AWS_REGION \
        --query 'StackEvents[0:20].{Time:Timestamp,Status:ResourceStatus,Resource:LogicalResourceId,Reason:ResourceStatusReason}' \
        --output table
}

# 实时跟踪模式
follow_pipeline() {
    while true; do
        show_pipeline_info
        show_pipeline_status
        
        # 检查是否有正在进行的执行
        CURRENT_STATUS=$(aws codepipeline list-pipeline-executions --pipeline-name $PIPELINE_NAME --region $AWS_REGION --max-items 1 --query 'pipelineExecutionSummaries[0].status' --output text)
        
        if [[ "$CURRENT_STATUS" == "Succeeded" || "$CURRENT_STATUS" == "Failed" || "$CURRENT_STATUS" == "Stopped" ]]; then
            print_green "Pipeline执行完成，最终状态: $CURRENT_STATUS"
            break
        fi
        
        echo "⏳ 等待30秒后刷新..."
        sleep 30
    done
}

# 主逻辑
if [ "$FOLLOW_MODE" = true ]; then
    follow_pipeline
elif [ "$BUILD_LOGS" = true ]; then
    show_pipeline_info
    show_build_logs
elif [ "$DEPLOY_LOGS" = true ]; then
    show_pipeline_info
    show_deploy_logs
else
    show_pipeline_info
    show_pipeline_status
fi
