#!/bin/bash

# FixGuru 环境变量管理脚本
# 统一管理所有环境变量配置

set -e

# 导入颜色工具
source "$(dirname "$0")/colors.sh"

# 配置变量
ENV_FILE=".env.dev"
BACKUP_FILE=".env.aws.backup"

print_info "🔧 FixGuru 环境变量管理"
echo ""

# 备份现有配置
if [ -f "$ENV_FILE" ]; then
    cp "$ENV_FILE" "$BACKUP_FILE"
    print_warning "📋 已备份当前配置到: $BACKUP_FILE"
fi

# 初始化环境文件
cat > "$ENV_FILE" << 'EOF'
# FixGuru AWS环境变量配置文件
# 由env-manager.sh自动生成，请勿手动编辑
# 生成时间: 
EOF
echo "# $(date)" >> "$ENV_FILE"
echo "" >> "$ENV_FILE"

# 配置向导
echo -e "${BLUE}📋 开始配置环境变量...${NC}"

# 选择环境
echo ""
echo -e "${YELLOW}1. 环境配置${NC}"
PS3="请选择部署环境 (1-3): "
select env in "开发环境(dev)" "测试环境(qa)" "生产环境(prod)"; do
    case $env in
        "开发环境(dev)")
            export ENVIRONMENT="dev"
            break
            ;;
        "测试环境(qa)")
            export ENVIRONMENT="qa"
            break
            ;;
        "生产环境(prod)")
            export ENVIRONMENT="prod"
            break
            ;;
        *)
            echo -e "${RED}无效选择，请重新输入${NC}"
            ;;
    esac
done
echo -e "${GREEN}✅ 环境: $ENVIRONMENT${NC}"
echo "export ENVIRONMENT=$ENVIRONMENT" >> "$ENV_FILE"

# AWS区域配置
echo ""
echo -e "${YELLOW}2. AWS区域配置${NC}"
read -p "请输入AWS区域 (默认: us-west-1): " aws_region
aws_region=${aws_region:-us-west-1}
export AWS_REGION="$aws_region"
echo -e "${GREEN}✅ AWS区域: $aws_region${NC}"
echo "export AWS_REGION=$aws_region" >> "$ENV_FILE"

# DynamoDB配置
echo ""
echo -e "${YELLOW}3. DynamoDB配置${NC}"
read -p "请输入表前缀 (默认: ${ENVIRONMENT}_): " table_prefix
table_prefix=${table_prefix:-${ENVIRONMENT}_}
export TABLE_PREFIX="$table_prefix"
echo -e "${GREEN}✅ 表前缀: $table_prefix${NC}"
echo "export TABLE_PREFIX=$table_prefix" >> "$ENV_FILE"

# AWS凭证配置
echo ""
echo -e "${YELLOW}4. AWS凭证配置${NC}"
echo "配置DynamoDB访问凭证（可选，如不配置将使用默认凭证）："

read -p "DynamoDB访问密钥ID (可选): " dynamodb_access_key
if [ -n "$dynamodb_access_key" ]; then
    export DYNAMODB_ACCESS_KEY_ID="$dynamodb_access_key"
    echo "export DYNAMODB_ACCESS_KEY_ID=$dynamodb_access_key" >> "$ENV_FILE"
    echo -e "${GREEN}✅ DYNAMODB_ACCESS_KEY_ID=***${NC}"
else
    echo -e "${BLUE}ℹ️ 使用默认AWS凭证${NC}"
fi

read -p "DynamoDB秘密访问密钥 (可选): " dynamodb_secret_key
if [ -n "$dynamodb_secret_key" ]; then
    export DYNAMODB_SECRET_ACCESS_KEY="$dynamodb_secret_key"
    echo "export DYNAMODB_SECRET_ACCESS_KEY=$dynamodb_secret_key" >> "$ENV_FILE"
    echo -e "${GREEN}✅ DYNAMODB_SECRET_ACCESS_KEY=***${NC}"
fi

# S3配置
echo ""
echo -e "${YELLOW}5. S3配置${NC}"
read -p "S3存储桶名称 (可选): " s3_bucket
if [ -n "$s3_bucket" ]; then
    export S3_BUCKET="$s3_bucket"
    echo "export S3_BUCKET=$s3_bucket" >> "$ENV_FILE"
    echo -e "${GREEN}✅ S3_BUCKET=$s3_bucket${NC}"
fi

read -p "S3区域 (默认: $aws_region): " s3_region
s3_region=${s3_region:-$aws_region}
export S3_REGION="$s3_region"
echo "export S3_REGION=$s3_region" >> "$ENV_FILE"
echo -e "${GREEN}✅ S3_REGION=$s3_region${NC}"

# CloudWatch配置
echo ""
echo -e "${YELLOW}6. CloudWatch配置${NC}"
read -p "CloudWatch日志组名称 (可选): " cloudwatch_log_group
if [ -n "$cloudwatch_log_group" ]; then
    export CLOUDWATCH_LOG_GROUP="$cloudwatch_log_group"
    echo "export CLOUDWATCH_LOG_GROUP=$cloudwatch_log_group" >> "$ENV_FILE"
    echo -e "${GREEN}✅ CLOUDWATCH_LOG_GROUP=$cloudwatch_log_group${NC}"
fi

read -p "CloudWatch区域 (默认: $aws_region): " cloudwatch_region
cloudwatch_region=${cloudwatch_region:-$aws_region}
export CLOUDWATCH_REGION="$cloudwatch_region"
echo "export CLOUDWATCH_REGION=$cloudwatch_region" >> "$ENV_FILE"
echo -e "${GREEN}✅ CLOUDWATCH_REGION=$cloudwatch_region${NC}"

# Lambda配置
echo ""
echo -e "${YELLOW}7. Lambda配置${NC}"
read -p "Lambda执行角色ARN (可选): " lambda_role_arn
if [ -n "$lambda_role_arn" ]; then
    export LAMBDA_ROLE_ARN="$lambda_role_arn"
    echo "export LAMBDA_ROLE_ARN=$lambda_role_arn" >> "$ENV_FILE"
    echo -e "${GREEN}✅ LAMBDA_ROLE_ARN=***${NC}"
fi

read -p "API Gateway阶段 (默认: $ENVIRONMENT): " api_gateway_stage
api_gateway_stage=${api_gateway_stage:-$ENVIRONMENT}
export API_GATEWAY_STAGE="$api_gateway_stage"
echo "export API_GATEWAY_STAGE=$api_gateway_stage" >> "$ENV_FILE"
echo -e "${GREEN}✅ API_GATEWAY_STAGE=$api_gateway_stage${NC}"

# 应用配置
echo ""
echo -e "${YELLOW}8. 应用配置${NC}"
read -p "JWT密钥 (可选): " jwt_secret
if [ -n "$jwt_secret" ]; then
    export JWT_SECRET="$jwt_secret"
    echo "export JWT_SECRET=$jwt_secret" >> "$ENV_FILE"
    echo -e "${GREEN}✅ JWT_SECRET=***${NC}"
fi

read -p "Swagger用户名 (默认: admin): " swagger_username
swagger_username=${swagger_username:-admin}
export SWAGGER_USERNAME="$swagger_username"
echo "export SWAGGER_USERNAME=$swagger_username" >> "$ENV_FILE"
echo -e "${GREEN}✅ SWAGGER_USERNAME=$swagger_username${NC}"

read -p "Swagger密码 (默认: fixguru123): " swagger_password
swagger_password=${swagger_password:-fixguru123}
export SWAGGER_PASSWORD="$swagger_password"
echo "export SWAGGER_PASSWORD=$swagger_password" >> "$ENV_FILE"
echo -e "${GREEN}✅ SWAGGER_PASSWORD=***${NC}"

# 高级配置
echo ""
echo -e "${YELLOW}9. 高级配置${NC}"
read -p "是否启用详细日志? (y/N): " enable_debug
if [[ $enable_debug =~ ^[Yy]$ ]]; then
    export LOGGING_LEVEL_ROOT="DEBUG"
    echo "export LOGGING_LEVEL_ROOT=DEBUG" >> "$ENV_FILE"
    echo -e "${GREEN}✅ 已启用详细日志${NC}"
else
    export LOGGING_LEVEL_ROOT="INFO"
    echo "export LOGGING_LEVEL_ROOT=INFO" >> "$ENV_FILE"
    echo -e "${GREEN}✅ 日志级别: INFO${NC}"
fi

# 显示配置摘要
echo ""
echo -e "${BLUE}📋 配置摘要${NC}"
echo -e "${YELLOW}环境: ${NC}$ENVIRONMENT"
echo -e "${YELLOW}AWS区域: ${NC}$aws_region"
echo -e "${YELLOW}表前缀: ${NC}$table_prefix"
echo -e "${YELLOW}S3区域: ${NC}$s3_region"
echo -e "${YELLOW}CloudWatch区域: ${NC}$cloudwatch_region"
echo -e "${YELLOW}API Gateway阶段: ${NC}$api_gateway_stage"
echo -e "${YELLOW}Swagger用户名: ${NC}$swagger_username"

# 询问是否保存配置
echo ""
read -p "是否保存配置? (Y/n): " save_config
save_config=${save_config:-Y}

if [[ $save_config =~ ^[Yy]$ ]]; then
    echo "export SPRING_PROFILES_ACTIVE=aws" >> "$ENV_FILE"
    
    echo ""
    print_success "配置已保存到: $ENV_FILE"
    print_info "💡 使用方法:"
    echo "   source $ENV_FILE"
    echo ""
    print_warning "下一步操作:"
    echo "1. 部署应用: ./scripts/deploy.sh dev"
    echo "2. 或设置CI/CD: ./scripts/deploy-pipeline.sh dev"
else
    # 恢复备份配置
    if [ -f "$BACKUP_FILE" ]; then
        mv "$BACKUP_FILE" "$ENV_FILE"
        echo -e "${YELLOW}🔄 已恢复之前的配置${NC}"
    else
        rm -f "$ENV_FILE"
        print_warning "🗑️ 已删除临时配置文件"
    fi
fi

echo ""
print_success "🎉 环境变量配置完成！"
