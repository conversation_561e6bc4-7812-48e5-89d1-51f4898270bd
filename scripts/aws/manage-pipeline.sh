#!/bin/bash

# FixGuru Pipeline 管理脚本
# 用于管理CodePipeline的常用操作

set -e

# 导入颜色工具
source "$(dirname "$0")/colors.sh"

# 脚本信息
SCRIPT_NAME="FixGuru Pipeline Manager"
VERSION="1.0.0"

print_header() {
    echo
    print_blue "=================================="
    print_blue "  $SCRIPT_NAME v$VERSION"
    print_blue "=================================="
    echo
}

print_usage() {
    echo "用法: $0 <COMMAND> [ENVIRONMENT]"
    echo
    echo "命令:"
    echo "  status     查看Pipeline状态"
    echo "  start      手动触发Pipeline执行"
    echo "  stop       停止Pipeline执行"
    echo "  logs       查看构建日志"
    echo "  history    查看执行历史"
    echo "  delete     删除Pipeline"
    echo
    echo "环境:"
    echo "  dev        开发环境"
    echo "  test       测试环境"
    echo "  prod       生产环境"
    echo
    echo "示例:"
    echo "  $0 status dev      # 查看开发环境Pipeline状态"
    echo "  $0 start prod      # 手动触发生产环境Pipeline"
    echo "  $0 logs test       # 查看测试环境构建日志"
    echo
}

# 检查参数
if [ $# -lt 1 ]; then
    print_usage
    exit 1
fi

COMMAND=$1
ENVIRONMENT=${2:-"dev"}

# 验证环境参数
case $ENVIRONMENT in
    dev|test|prod)
        ;;
    *)
        print_red "✗ 无效的环境参数: $ENVIRONMENT"
        print_usage
        exit 1
        ;;
esac

# 设置变量
PROJECT_NAME="fixguru"
AWS_REGION=${AWS_REGION:-"us-west-1"}
PIPELINE_NAME="${PROJECT_NAME}-pipeline-${ENVIRONMENT}"
BUILD_PROJECT="${PROJECT_NAME}-build-${ENVIRONMENT}"
APP_STACK="${PROJECT_NAME}-app-${ENVIRONMENT}"

print_header

# 检查AWS凭证
print_blue "检查AWS凭证..."
if ! aws sts get-caller-identity >/dev/null 2>&1; then
    print_red "✗ AWS凭证未配置或无效"
    exit 1
fi
print_green "✓ AWS凭证有效"

# 执行命令
case $COMMAND in
    status)
        print_blue "查看Pipeline状态: $PIPELINE_NAME"
        echo
        
        # 检查Pipeline是否存在
        if ! aws codepipeline get-pipeline --name $PIPELINE_NAME --region $AWS_REGION >/dev/null 2>&1; then
            print_red "✗ Pipeline不存在: $PIPELINE_NAME"
            exit 1
        fi
        
        # 获取Pipeline状态
        PIPELINE_STATE=$(aws codepipeline get-pipeline-state --name $PIPELINE_NAME --region $AWS_REGION)
        
        echo "Pipeline名称: $PIPELINE_NAME"
        echo "AWS区域: $AWS_REGION"
        echo
        
        # 显示各阶段状态
        echo "$PIPELINE_STATE" | jq -r '.stageStates[] | "阶段: \(.stageName) | 状态: \(.latestExecution.status // "未执行")"'
        
        # 显示最新执行信息
        LATEST_EXECUTION=$(aws codepipeline list-pipeline-executions --pipeline-name $PIPELINE_NAME --region $AWS_REGION --max-items 1 --query 'pipelineExecutionSummaries[0]')
        
        if [ "$LATEST_EXECUTION" != "null" ]; then
            echo
            print_blue "最新执行信息:"
            echo "$LATEST_EXECUTION" | jq -r '"执行ID: \(.pipelineExecutionId)"'
            echo "$LATEST_EXECUTION" | jq -r '"状态: \(.status)"'
            echo "$LATEST_EXECUTION" | jq -r '"开始时间: \(.startTime)"'
        fi
        ;;
        
    start)
        print_blue "手动触发Pipeline: $PIPELINE_NAME"
        
        EXECUTION_ID=$(aws codepipeline start-pipeline-execution --name $PIPELINE_NAME --region $AWS_REGION --query 'pipelineExecutionId' --output text)
        
        if [ $? -eq 0 ]; then
            print_green "✓ Pipeline已触发"
            echo "执行ID: $EXECUTION_ID"
            echo "监控URL: https://console.aws.amazon.com/codesuite/codepipeline/pipelines/$PIPELINE_NAME/view"
        else
            print_red "✗ Pipeline触发失败"
            exit 1
        fi
        ;;
        
    stop)
        print_blue "停止Pipeline执行: $PIPELINE_NAME"
        
        # 获取正在执行的Pipeline
        RUNNING_EXECUTION=$(aws codepipeline list-pipeline-executions --pipeline-name $PIPELINE_NAME --region $AWS_REGION --query 'pipelineExecutionSummaries[?status==`InProgress`][0].pipelineExecutionId' --output text)
        
        if [ "$RUNNING_EXECUTION" = "None" ] || [ -z "$RUNNING_EXECUTION" ]; then
            print_yellow "⚠ 没有正在执行的Pipeline"
            exit 0
        fi
        
        aws codepipeline stop-pipeline-execution --pipeline-name $PIPELINE_NAME --pipeline-execution-id $RUNNING_EXECUTION --region $AWS_REGION --abandon
        
        if [ $? -eq 0 ]; then
            print_green "✓ Pipeline执行已停止"
            echo "执行ID: $RUNNING_EXECUTION"
        else
            print_red "✗ Pipeline停止失败"
            exit 1
        fi
        ;;
        
    logs)
        print_blue "查看构建日志: $BUILD_PROJECT"
        
        # 获取最新构建ID
        BUILD_ID=$(aws codebuild list-builds-for-project --project-name $BUILD_PROJECT --region $AWS_REGION --query 'ids[0]' --output text)
        
        if [ "$BUILD_ID" = "None" ] || [ -z "$BUILD_ID" ]; then
            print_yellow "⚠ 没有找到构建记录"
            exit 0
        fi
        
        echo "最新构建ID: $BUILD_ID"
        echo "日志组: /aws/codebuild/$BUILD_PROJECT"
        echo
        
        # 显示日志
        aws logs get-log-events --log-group-name "/aws/codebuild/$BUILD_PROJECT" --log-stream-name "$BUILD_ID" --region $AWS_REGION --query 'events[].message' --output text
        ;;
        
    history)
        print_blue "查看执行历史: $PIPELINE_NAME"
        
        aws codepipeline list-pipeline-executions --pipeline-name $PIPELINE_NAME --region $AWS_REGION --max-items 10 --query 'pipelineExecutionSummaries[].[pipelineExecutionId,status,startTime]' --output table
        ;;
        
    delete)
        print_red "⚠ 危险操作：删除Pipeline"
        echo "这将删除以下资源："
        echo "- Pipeline: $PIPELINE_NAME"
        echo "- CodeBuild项目: $BUILD_PROJECT"
        echo "- 相关IAM角色和S3存储桶"
        echo
        
        read -p "确认删除? (输入 'DELETE' 确认): " CONFIRM
        
        if [ "$CONFIRM" != "DELETE" ]; then
            print_yellow "操作已取消"
            exit 0
        fi
        
        print_blue "删除Pipeline基础设施..."
        
        # 删除Pipeline CloudFormation堆栈
        PIPELINE_STACK="${PROJECT_NAME}-pipeline-${ENVIRONMENT}"
        aws cloudformation delete-stack --stack-name $PIPELINE_STACK --region $AWS_REGION
        
        print_green "✓ 删除命令已发送"
        echo "请在AWS控制台监控删除进度"
        ;;
        
    *)
        print_red "✗ 未知命令: $COMMAND"
        print_usage
        exit 1
        ;;
esac
