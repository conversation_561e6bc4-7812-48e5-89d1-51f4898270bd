# FixGuru

基于AWS DynamoDB和Lambda的企业级维修服务平台，支持本地开发和云端部署。

## 📋 项目概述

### 项目特点
- **Spring Boot 3.2.5** + **JDK 17** 现代化技术栈
- **AWS DynamoDB** NoSQL数据库，支持多环境部署
- **AWS Lambda** 无服务器架构，全球CDN加速
- **Knife4j** API文档自动生成
- **JWT认证** 完整的用户管理和权限控制
- **CI/CD支持** 自动化部署流水线

### 技术架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   CloudFront    │    │   API Gateway   │
│   (iOS/Web)     │───▶│   (全球CDN)     │───▶│   (EDGE优化)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                               ┌─────────────────┐
                                               │   AWS Lambda    │
                                               │   Spring Boot   │
                                               └─────────────────┘
                                                        │
                                               ┌─────────────────┐
                                               │   DynamoDB      │
                                               │  (主数据库)      │
                                               └─────────────────┘
```

## 🚀 快速开始

### 环境要求
- **JDK 17+**
- **Maven 3.9.6+**
- **AWS CLI** (已配置)
- **Git**

### 1. 克隆项目
```bash
git clone https://github.com/DayuHZ/FixGuru_Service.git
cd FixGuru_Service
```

### 2. 本地开发
```bash
# 编译项目
mvn clean compile

# 启动应用（开发环境）
mvn spring-boot:run -Dspring.profiles.active=dev
```

### 3. 验证服务
- **API文档**: http://localhost:8080/api/doc.html
- **健康检查**: http://localhost:8080/api/v1/health

## 📚 API接口说明

### 健康检查接口
```bash
# 基础健康检查
curl http://localhost:8080/api/v1/health

# 详细健康检查
curl http://localhost:8080/api/v1/health/detailed
```

### 用户管理接口

#### 1. 用户注册
```bash
curl -X POST http://localhost:8080/api/v1/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "testuser",
    "email": "<EMAIL>",
    "password": "Test123456",
    "confirmPassword": "Test123456"
  }'
```

#### 2. 用户登录
```bash
curl -X POST http://localhost:8080/api/v1/users/login \
  -H "Content-Type: application/json" \
  -d '{
    "account": "testuser",
    "password": "Test123456"
  }'
```

#### 3. 获取用户信息（需要认证）
```bash
# 根据用户ID获取
curl -H "Authorization: access_your_jwt_token" \
  http://localhost:8080/api/v1/users/{userId}

# 根据用户名获取
curl -H "Authorization: access_your_jwt_token" \
  http://localhost:8080/api/v1/users/username/{username}
```

#### 4. 检查用户名/邮箱是否存在
```bash
# 检查用户名
curl http://localhost:8080/api/v1/users/check/username/{username}

# 检查邮箱
curl http://localhost:8080/api/v1/users/check/email/{email}
```

## 🔧 本地开发配置

### 环境配置文件
- **开发环境**: `src/main/resources/application-dev.yml`
- **测试环境**: `src/main/resources/application-test.yml`
- **生产环境**: `src/main/resources/application-prod.yml`
- **主配置**: `src/main/resources/application.yml`

### 开发工具
- **API文档**: Knife4j提供交互式API文档
- **JWT认证**: 基于注解的安全认证系统
- **健康检查**: 系统状态监控接口
- **日志**: 分环境的日志级别配置

## ☁️ AWS部署

### 🎯 部署策略（重要）

为了节省AWS CodePipeline额度，项目采用**手动触发**策略：

**📱 日常开发部署（推荐）**
- 使用：`./scripts/deploy.sh dev`
- 场景：功能开发、bug修复、日常调试
- 优势：快速部署，不消耗Pipeline额度
- 适用：90%的开发场景

**🚀 正式发布部署**
- 使用：`./scripts/aws/manage-pipeline.sh start prod`
- 场景：版本发布、生产部署、重要更新
- 优势：完整CI/CD流程、测试覆盖、审计记录
- 适用：正式发布、生产环境

**⚠️ 重要提醒**：git push 不会自动触发Pipeline，你可以安全地提交代码！

### 部署步骤

#### 1. 配置AWS凭证
```bash
aws configure
```

#### 2. 快速部署
```bash
# 部署到开发环境
./scripts/deploy.sh dev

# 部署到测试环境
./scripts/deploy.sh test

# 部署到生产环境
./scripts/deploy.sh prod
```

#### 3. 验证部署
```bash
# 检查应用健康状态
curl -X GET "${API_GATEWAY_URL}/api/v1/health"

# 访问API文档
open "${API_GATEWAY_URL}/doc.html"
```

## 🧪 测试和验证

### 本地测试
```bash
# 编译项目
mvn clean compile

# 运行单元测试
mvn test
```

### API测试
1. 启动应用后访问: http://localhost:8080/api/doc.html
2. 使用Knife4j交互式API文档进行测试
3. 参考API文档了解认证方式和接口参数

## 📁 项目结构
```
FixGuru_Service/
├── src/
│   ├── main/
│   │   ├── java/com/fixguru/
│   │   │   ├── controller/          # REST控制器
│   │   │   ├── service/             # 业务服务层
│   │   │   ├── domain/              # 数据实体
│   │   │   ├── dto/                 # 数据传输对象
│   │   │   ├── config/              # 配置类
│   │   │   ├── aws/                 # AWS服务集成
│   │   │   ├── common/              # 通用组件
│   │   │   ├── constants/           # 常量定义
│   │   │   ├── exception/           # 异常处理
│   │   │   ├── utils/               # 工具类
│   │   │   └── enums/               # 枚举定义
│   │   └── resources/               # 配置文件
│   └── assembly/                    # 构建配置
├── docs/                            # 项目文档
├── scripts/                         # 部署脚本
│   ├── aws/                         # AWS管理脚本
│   ├── deploy.sh                    # 手动部署脚本
│   └── deploy-pipeline.sh           # CI/CD部署脚本
├── target/                          # 构建输出
├── buildspec.yml                    # CodeBuild配置
├── pipeline-template.yaml           # CodePipeline模板
├── template.yaml                    # SAM应用模板
├── pom.xml                         # Maven配置
└── README.md                       # 项目说明
```

## 🔍 常见问题

### Q: 本地启动失败？
A: 检查JDK版本是否为17+，确保Maven配置正确。

### Q: 无法连接到DynamoDB？
A: 确认AWS凭证配置正确，检查网络连接和权限设置。

### Q: Lambda部署后无法访问？
A: 检查IAM角色权限，确保Lambda有访问DynamoDB的权限。

### Q: API文档无法访问？
A: 确认Knife4j配置正确，检查应用是否正常启动。

### Q: JWT认证失败？
A: 检查token格式是否正确，确保在请求头中使用 `Authorization: access_your_jwt_token`。

## 🚀 CI/CD 自动化部署

### 快速设置CI/CD流水线
```bash
# 1. 部署CI/CD流水线（一键完成）
./scripts/deploy-pipeline.sh dev

# 2. 手动触发构建（不会自动触发）
./scripts/aws/manage-pipeline.sh start dev

# 3. 监控Pipeline状态
./scripts/aws/manage-pipeline.sh status dev
```

### Pipeline管理命令
```bash
# 查看Pipeline状态
./scripts/aws/manage-pipeline.sh status dev

# 手动触发构建
./scripts/aws/manage-pipeline.sh start dev

# 查看构建日志
./scripts/aws/manage-pipeline.sh logs dev

# 故障排查
./scripts/aws/troubleshoot-pipeline.sh dev
```

### 📋 CI/CD特性
- ✅ **手动触发**：git push 不会自动触发，节省Pipeline额度
- ✅ **多环境支持**：dev/test/prod环境独立管理
- ✅ **安全管理**：GitHub Token和JWT密钥自动管理
- ✅ **构建缓存**：Maven依赖缓存加速构建
- ✅ **全球CDN**：EDGE优化的API Gateway，全球访问加速

## 📚 详细文档

### 🏗️ 架构设计
- **[初始架构设计文档](docs/初始架构设计文档.md)** - 项目架构设计参考

### 🚀 部署与开发
- **[AWS部署与开发指南](docs/AWS部署与开发指南.md)** - AWS Lambda部署、环境配置、开发工具
- **[CICD部署指南](docs/CICD部署指南.md)** - CodePipeline自动化部署配置

## 📞 技术支持

### 问题排查顺序
1. **本地问题**：检查JDK版本和Maven配置
2. **部署失败**：查看CloudFormation事件
3. **API问题**：检查Lambda函数日志
4. **Pipeline问题**：使用故障排查脚本

### 联系方式
- **项目仓库**: https://github.com/DayuHZ/FixGuru_Service
- **问题反馈**: 提交GitHub Issue

## 📄 许可证

本项目采用 Apache 2.0 许可证。